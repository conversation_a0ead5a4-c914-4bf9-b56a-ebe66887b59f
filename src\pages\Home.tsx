import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { supabase } from '@/lib/supabase';
import AnnouncementPopup from '../components/announcements/AnnouncementPopup';
import { useAnnouncementDismissal } from '@/hooks/useAnnouncementDismissal';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, MapPin, Users, Clock, Star, Bell, Activity, TrendingUp, Heart, Eye } from 'lucide-react';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import type { Announcement } from '@/types';
import type { Database } from '@/types/supabase';
import { AnnouncementDisplayType, AnnouncementPriority } from '@/types/announcements';

type AnnouncementRow = Database['public']['Tables']['announcements']['Row'] & {
  category?: Array<{
    id: string;
    name: string;
    color: string | null;
    description: string | null;
    created_at: string;
    updated_at: string;
  }>;
};

interface Festival {
  id: string;
  name: string;
  description: string | null;
  start_date?: string;
  end_date?: string;
  location?: string;
  featured?: boolean;
}

interface Activity {
  id: string;
  title: string;
  description: string;
  type: string;
  location?: string | null;
  start_date?: string | null;
  end_date?: string | null;
  capacity?: number | null;
  is_featured?: boolean | null;
  festival_id?: string | null;
}

interface Tip {
  id: string;
  title: string;
  description?: string | null;
  category: string | null;
  is_featured?: boolean | null;
  helpful_count?: number | null;
  view_count?: number | null;
}

interface Guide {
  id: string;
  title: string;
  description?: string | null;
  category: string | null;
  is_featured?: boolean | null;
  view_count?: number | null;
}

const Home: React.FC = () => {
  const { user } = useAuth();
  const [showAnnouncement, setShowAnnouncement] = useState(false);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  
  // New state for dashboard data
  const [todaysActivities, setTodaysActivities] = useState<Activity[]>([]);
  const [featuredTips, setFeaturedTips] = useState<Tip[]>([]);
  const [featuredGuides, setFeaturedGuides] = useState<Guide[]>([]);
  const [recentAnnouncements, setRecentAnnouncements] = useState<AnnouncementRow[]>([]);
  const [loading, setLoading] = useState(true);

  // Announcement dismissal
  const {
    dismissAnnouncement,
    isDismissed
  } = useAnnouncementDismissal();

  // Get today's date for filtering
  const today = new Date().toISOString().split('T')[0];

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch today's activities
      const { data: activities } = await supabase
        .from('activities')
        .select('*')
        .gte('start_date', today)
        .lte('start_date', `${today}T23:59:59`)
        .order('start_date', { ascending: true })
        .limit(6);

      // Fetch featured tips
      const { data: tips } = await supabase
        .from('tips')
        .select('*')
        .eq('is_featured', true)
        .eq('status', 'published')
        .order('helpful_count', { ascending: false })
        .limit(4);

      // Fetch featured guides
      const { data: guides } = await supabase
        .from('guides')
        .select('*')
        .eq('is_featured', true)
        .eq('status', 'published')
        .order('view_count', { ascending: false })
        .limit(4);

      // Fetch recent announcements
      const { data: announcementsData } = await supabase
        .from('announcements')
        .select('*')
        .eq('active', true)
        .order('created_at', { ascending: false })
        .limit(3);

      setTodaysActivities(activities || []);
      setFeaturedTips(tips || []);
      setFeaturedGuides(guides || []);
      setRecentAnnouncements(announcementsData || []);
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch popup announcements
  const fetchAnnouncements = async () => {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .eq('active', true)
        .eq('display_type', 'popup')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;

      const mappedAnnouncements: Announcement[] = (data || []).map(row => ({
        ...row,
        priority: (row.priority as AnnouncementPriority) || 'NORMAL',
        target_audience: row.target_audience as string[] || [],
        active: row.active || false,
        display_type: row.display_type as AnnouncementDisplayType,
      }));

      const activeAnnouncements = mappedAnnouncements.filter(ann => 
        ann.active && !isDismissed(ann.id.toString())
      );

      setAnnouncements(activeAnnouncements);
      
      if (activeAnnouncements.length > 0) {
        setShowAnnouncement(true);
      }
    } catch (error) {
      console.error('Error fetching announcements:', error);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    fetchAnnouncements();
  }, []);

  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-sunset flex items-center justify-center">
        <div className="text-neutral-800 text-xl font-medium">Loading your festival dashboard...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-sunset">
      {/* Popup Announcement */}
      {showAnnouncement && announcements.length > 0 && (
        <AnnouncementPopup
          announcement={{
            ...announcements[currentIndex],
            id: announcements[currentIndex].id.toString(),
            priority: 'NORMAL' as AnnouncementPriority,
            created_by: '',
            is_active: true,
            active: announcements[currentIndex].active || undefined,
            is_featured: announcements[currentIndex].is_featured || undefined,
            target_audience: announcements[currentIndex].target_audience || undefined,
            category_id: announcements[currentIndex].category_id || undefined,
            notification_sent: announcements[currentIndex].notification_sent || undefined,
            created_at: announcements[currentIndex].created_at || new Date().toISOString(),
            updated_at: announcements[currentIndex].updated_at ?? new Date().toISOString(),
            display_type: announcements[currentIndex].display_type as any
          }}
          isOpen={showAnnouncement}
          onClose={() => setShowAnnouncement(false)}
          onDismiss={() => dismissAnnouncement(announcements[currentIndex].id.toString())}
        />
      )}

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-6xl font-bold text-neutral-800 mb-4 text-festival-gradient">
            Welcome back, {user?.user_metadata?.full_name || 'Festival Family'}! 🎪
          </h1>
          <p className="text-neutral-700 text-xl font-medium">Here's what's happening in your festival world</p>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
        >
          <Card className="bg-neutral-50/90 backdrop-blur-md border-neutral-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6 text-center">
              <Activity className="w-10 h-10 mx-auto mb-3 text-festival-orange" />
              <div className="text-3xl font-bold text-neutral-800 mb-1">{todaysActivities.length}</div>
              <div className="text-sm text-neutral-600 font-medium">Today's Activities</div>
            </CardContent>
          </Card>
          <Card className="bg-neutral-50/90 backdrop-blur-md border-neutral-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6 text-center">
              <TrendingUp className="w-10 h-10 mx-auto mb-3 text-festival-teal" />
              <div className="text-3xl font-bold text-neutral-800 mb-1">{featuredTips.length}</div>
              <div className="text-sm text-neutral-600 font-medium">Featured Tips</div>
            </CardContent>
          </Card>
          <Card className="bg-neutral-50/90 backdrop-blur-md border-neutral-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6 text-center">
              <Bell className="w-10 h-10 mx-auto mb-3 text-festival-purple" />
              <div className="text-3xl font-bold text-neutral-800 mb-1">{recentAnnouncements.length}</div>
              <div className="text-sm text-neutral-600 font-medium">New Updates</div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Today's Activities */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="bg-neutral-50/95 backdrop-blur-md border-neutral-200/50 shadow-lg h-fit">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-neutral-800">
                  <Calendar className="w-6 h-6 text-festival-orange" />
                  Happening Today
                </CardTitle>
              </CardHeader>
              <CardContent>
                {todaysActivities.length > 0 ? (
                  <div className="space-y-4">
                    {todaysActivities.map((activity) => (
                      <div key={activity.id} className="bg-neutral-100/80 rounded-xl p-4 border border-neutral-200/50 hover:shadow-md transition-all duration-200">
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="font-semibold text-base text-neutral-800 line-clamp-1">{activity.title}</h3>
                          <Badge variant="outline" className="text-xs border-festival-orange text-festival-orange bg-festival-orange/10">
                            {activity.type}
                          </Badge>
                        </div>
                        <p className="text-neutral-600 text-sm mb-3 line-clamp-2">{activity.description}</p>
                        <div className="flex items-center gap-4 text-xs text-neutral-500">
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {formatTime(activity.start_date || '')}
                          </div>
                          {activity.location && (
                            <div className="flex items-center gap-1">
                              <MapPin className="w-4 h-4" />
                              <span className="line-clamp-1">{activity.location}</span>
                            </div>
                          )}
                          {activity.capacity && (
                            <div className="flex items-center gap-1">
                              <Users className="w-4 h-4" />
                              {activity.capacity} spots
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    <Link to="/activities">
                      <Button variant="outline" className="w-full text-neutral-700 border-neutral-300 hover:bg-neutral-100 hover:text-neutral-800 transition-colors">
                        View All Activities
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Calendar className="w-16 h-16 mx-auto mb-4 text-neutral-400" />
                    <p className="text-neutral-600 mb-6 text-lg">No activities scheduled for today</p>
                    <Link to="/activities">
                      <Button variant="outline" className="text-neutral-700 border-neutral-300 hover:bg-neutral-100 hover:text-neutral-800 transition-colors px-6 py-2">
                        Browse Activities
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Updates */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="bg-neutral-50/95 backdrop-blur-md border-neutral-200/50 shadow-lg h-fit">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-neutral-800">
                  <Bell className="w-6 h-6 text-festival-purple" />
                  Latest Updates
                </CardTitle>
              </CardHeader>
              <CardContent>
                {recentAnnouncements.length > 0 ? (
                  <div className="space-y-4">
                    {recentAnnouncements.map((announcement) => (
                      <div key={announcement.id} className="bg-neutral-100/80 rounded-xl p-4 border border-neutral-200/50 hover:shadow-md transition-all duration-200">
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="font-semibold text-base text-neutral-800 line-clamp-1">{announcement.title}</h3>
                          {announcement.priority && (
                            <Badge
                              variant="outline"
                              className={`text-xs ${
                                announcement.priority === 'high'
                                  ? 'border-error text-error bg-error/10'
                                  : announcement.priority === 'medium'
                                  ? 'border-warning text-warning bg-warning/10'
                                  : 'border-festival-purple text-festival-purple bg-festival-purple/10'
                              }`}
                            >
                              {announcement.priority}
                            </Badge>
                          )}
                        </div>
                        <p className="text-neutral-600 text-sm line-clamp-2 mb-3">{announcement.content}</p>
                        <div className="text-xs text-neutral-500">
                          {formatDate(announcement.created_at || '')}
                        </div>
                      </div>
                    ))}
                    <Link to="/famhub">
                      <Button variant="outline" className="w-full text-neutral-700 border-neutral-300 hover:bg-neutral-100 hover:text-neutral-800 transition-colors">
                        View All Updates
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Bell className="w-16 h-16 mx-auto mb-4 text-neutral-400" />
                    <p className="text-neutral-600 text-lg">No recent updates</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Featured Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-8"
        >
          <h2 className="text-3xl font-bold text-neutral-800 mb-8 text-center">Featured Festival Resources</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Featured Tips */}
            <Card className="bg-neutral-50/95 backdrop-blur-md border-neutral-200/50 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-neutral-800">
                  <Star className="w-6 h-6 text-festival-teal" />
                  Top Tips
                </CardTitle>
              </CardHeader>
              <CardContent>
                {featuredTips.length > 0 ? (
                  <div className="space-y-4">
                    {featuredTips.slice(0, 3).map((tip) => (
                      <div key={tip.id} className="bg-neutral-100/80 rounded-xl p-4 border border-neutral-200/50 hover:shadow-md transition-all duration-200">
                        <h3 className="font-semibold text-base text-neutral-800 mb-2 line-clamp-1">{tip.title}</h3>
                        <p className="text-neutral-600 text-sm mb-3 line-clamp-2">{tip.description}</p>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs border-success text-success bg-success/10">
                            {tip.category}
                          </Badge>
                          <div className="flex items-center gap-3 text-xs text-neutral-500">
                            <div className="flex items-center gap-1">
                              <Heart className="w-4 h-4" />
                              {tip.helpful_count || 0}
                            </div>
                            <div className="flex items-center gap-1">
                              <Eye className="w-4 h-4" />
                              {tip.view_count || 0}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                    <Link to="/resources">
                      <Button variant="outline" className="w-full text-neutral-700 border-neutral-300 hover:bg-neutral-100 hover:text-neutral-800 transition-colors">
                        View All Tips
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Star className="w-16 h-16 mx-auto mb-4 text-neutral-400" />
                    <p className="text-neutral-600 text-lg">No featured tips available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Featured Guides */}
            <Card className="bg-neutral-50/95 backdrop-blur-md border-neutral-200/50 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-neutral-800">
                  <Star className="w-6 h-6 text-festival-pink" />
                  Popular Guides
                </CardTitle>
              </CardHeader>
              <CardContent>
                {featuredGuides.length > 0 ? (
                  <div className="space-y-4">
                    {featuredGuides.slice(0, 3).map((guide) => (
                      <div key={guide.id} className="bg-neutral-100/80 rounded-xl p-4 border border-neutral-200/50 hover:shadow-md transition-all duration-200">
                        <h3 className="font-semibold text-base text-neutral-800 mb-2 line-clamp-1">{guide.title}</h3>
                        <p className="text-neutral-600 text-sm mb-3 line-clamp-2">{guide.description}</p>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs border-festival-pink text-festival-pink bg-festival-pink/10">
                            {guide.category}
                          </Badge>
                          <div className="flex items-center gap-1 text-xs text-neutral-500">
                            <Eye className="w-4 h-4" />
                            {guide.view_count || 0} views
                          </div>
                        </div>
                      </div>
                    ))}
                    <Link to="/resources">
                      <Button variant="outline" className="w-full text-neutral-700 border-neutral-300 hover:bg-neutral-100 hover:text-neutral-800 transition-colors">
                        View All Guides
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Star className="w-16 h-16 mx-auto mb-4 text-neutral-400" />
                    <p className="text-neutral-600 text-lg">No featured guides available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-8 text-center"
        >
          <h2 className="text-3xl font-bold text-neutral-800 mb-8">Quick Actions</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <Link to="/discover">
              <Button variant="outline" className="w-full text-neutral-700 border-neutral-300 hover:bg-festival-orange hover:text-white hover:border-festival-orange transition-all duration-300 p-6 h-auto">
                <div className="flex flex-col items-center gap-3">
                  <Calendar className="w-8 h-8" />
                  <span className="text-base font-medium">Discover Events</span>
                </div>
              </Button>
            </Link>
            <Link to="/activities">
              <Button variant="outline" className="w-full text-neutral-700 border-neutral-300 hover:bg-festival-teal hover:text-white hover:border-festival-teal transition-all duration-300 p-6 h-auto">
                <div className="flex flex-col items-center gap-3">
                  <Activity className="w-8 h-8" />
                  <span className="text-base font-medium">Join Activities</span>
                </div>
              </Button>
            </Link>
            <Link to="/famhub">
              <Button variant="outline" className="w-full text-neutral-700 border-neutral-300 hover:bg-festival-purple hover:text-white hover:border-festival-purple transition-all duration-300 p-6 h-auto">
                <div className="flex flex-col items-center gap-3">
                  <Users className="w-8 h-8" />
                  <span className="text-base font-medium">FamHub</span>
                </div>
              </Button>
            </Link>
            <Link to="/resources">
              <Button variant="outline" className="w-full text-neutral-700 border-neutral-300 hover:bg-festival-pink hover:text-white hover:border-festival-pink transition-all duration-300 p-6 h-auto">
                <div className="flex flex-col items-center gap-3">
                  <Star className="w-8 h-8" />
                  <span className="text-base font-medium">Resources</span>
                </div>
              </Button>
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Home;
