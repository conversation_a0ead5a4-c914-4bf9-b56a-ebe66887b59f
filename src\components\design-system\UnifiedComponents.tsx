/**
 * Unified Component System for Festival Family
 * Single source of truth for all UI components
 * Uses only design-tokens.css variables - NO hardcoded colors or conflicting styles
 */

import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

// ===== UNIFIED BUTTON COMPONENT =====
interface UnifiedButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  priority?: 'high' | 'medium' | 'low';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

export const UnifiedButton: React.FC<UnifiedButtonProps> = ({
  variant = 'primary',
  size = 'md',
  priority,
  children,
  onClick,
  disabled = false,
  className = '',
  type = 'button'
}) => {
  const baseClasses = 'component-base font-medium focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-festival-gradient text-primary-dark hover:opacity-90',
    secondary: 'bg-secondary text-primary-dark hover:bg-opacity-80',
    outline: 'border-2 bg-transparent text-primary-light hover:bg-card',
    ghost: 'bg-transparent text-secondary-light hover:bg-card'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const priorityClass = priority ? `priority-${priority}` : '';

  return (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.02 }}
      whileTap={{ scale: disabled ? 1 : 0.98 }}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${priorityClass}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${className}
      `}
      onClick={onClick}
      disabled={disabled}
      type={type}
    >
      {children}
    </motion.button>
  );
};

// ===== UNIFIED CARD COMPONENT =====
interface UnifiedCardProps {
  variant?: 'default' | 'elevated' | 'outlined' | 'glass';
  priority?: 'high' | 'medium' | 'low';
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export const UnifiedCard: React.FC<UnifiedCardProps> = ({
  variant = 'default',
  priority,
  children,
  className = '',
  onClick
}) => {
  const baseClasses = 'component-base';
  
  const variantClasses = {
    default: 'bg-card border border-neutral-200',
    elevated: 'bg-card-light shadow-lg border-0',
    outlined: 'bg-transparent border-2 border-neutral-300',
    glass: 'bg-card bg-opacity-80 backdrop-blur-md border border-white border-opacity-20'
  };

  const priorityClass = priority ? `priority-${priority}` : '';
  const interactiveClass = onClick ? 'cursor-pointer hover:shadow-lg' : '';

  return (
    <motion.div
      whileHover={onClick ? { y: -2 } : {}}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${priorityClass}
        ${interactiveClass}
        ${className}
      `}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );
};

// ===== UNIFIED BADGE COMPONENT =====
interface UnifiedBadgeProps {
  variant?: 'default' | 'priority' | 'category';
  priority?: 'high' | 'medium' | 'low';
  children: React.ReactNode;
  className?: string;
}

export const UnifiedBadge: React.FC<UnifiedBadgeProps> = ({
  variant = 'default',
  priority,
  children,
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
  
  const variantClasses = {
    default: 'bg-neutral-100 text-neutral-800',
    priority: priority ? `priority-${priority}` : 'bg-neutral-100 text-neutral-800',
    category: 'bg-accent-purple-100 text-accent-purple-800'
  };

  return (
    <span className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
      {children}
    </span>
  );
};

// ===== UNIFIED ICON BUTTON =====
interface UnifiedIconButtonProps {
  icon: LucideIcon;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  'aria-label': string;
}

export const UnifiedIconButton: React.FC<UnifiedIconButtonProps> = ({
  icon: Icon,
  variant = 'ghost',
  size = 'md',
  onClick,
  disabled = false,
  className = '',
  'aria-label': ariaLabel
}) => {
  const baseClasses = 'component-base flex items-center justify-center focus:outline-none focus:ring-2';
  
  const variantClasses = {
    primary: 'bg-festival-gradient text-primary-dark hover:opacity-90',
    secondary: 'bg-secondary text-primary-dark hover:bg-opacity-80',
    ghost: 'bg-transparent text-secondary-light hover:bg-card'
  };

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24
  };

  return (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${className}
      `}
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
    >
      <Icon size={iconSizes[size]} />
    </motion.button>
  );
};

// ===== UNIFIED CONTAINER =====
interface UnifiedContainerProps {
  variant?: 'page' | 'section' | 'content';
  children: React.ReactNode;
  className?: string;
}

export const UnifiedContainer: React.FC<UnifiedContainerProps> = ({
  variant = 'content',
  children,
  className = ''
}) => {
  const variantClasses = {
    page: 'min-h-screen bg-dark-gradient',
    section: 'py-8 px-4',
    content: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'
  };

  return (
    <div className={`${variantClasses[variant]} ${className}`}>
      {children}
    </div>
  );
};

// ===== UNIFIED ANNOUNCEMENT COMPONENT =====
interface UnifiedAnnouncementProps {
  priority: 'high' | 'medium' | 'low';
  title: string;
  message: string;
  onClose?: () => void;
  className?: string;
}

export const UnifiedAnnouncement: React.FC<UnifiedAnnouncementProps> = ({
  priority,
  title,
  message,
  onClose,
  className = ''
}) => {
  return (
    <UnifiedCard variant="outlined" priority={priority} className={`p-4 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="font-semibold text-sm mb-1">{title}</h3>
          <p className="text-sm opacity-90">{message}</p>
        </div>
        {onClose && (
          <UnifiedIconButton
            icon={() => <span>×</span>}
            variant="ghost"
            size="sm"
            onClick={onClose}
            aria-label="Close announcement"
          />
        )}
      </div>
    </UnifiedCard>
  );
};
