import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DiscoverFilters } from '@/types/constants';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ErrorBoundary } from 'react-error-boundary';
import { AlertCircle, RefreshCw, Search, Filter, Calendar, MapPin, Users, Heart, Share2, ChevronRight, Star, Compass } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { FestivalType } from '@/types/core';
import { Button } from '@/components/ui/button';
import { useReducedMotion, motionClasses } from '@/hooks/ui/useReducedMotion';
import { toast } from 'react-hot-toast';
import { simulateHapticFeedback, isMobileViewport, createTouchHandler } from '../utils/mobileUX';
import MobileUXTester from '../components/testing/MobileUXTester';
import FallbackImage from '@/components/ui/FallbackImage';
import { EventDetailsModal } from '@/components/design-system/EventDetailsModal';
// Import unified design system components
import {
  PageWrapper,
  UnifiedCard,
  UnifiedButton,
  UnifiedBadge,
  GridLayout,
  FlexLayout
} from '@/components/design-system';

// Enhanced Mobile-First Event Card component
interface EventCardProps {
  event: any;
  onClick: (element: HTMLElement | null) => void;
  index?: number;
}

const EventCard = React.memo(({ event, onClick, index = 0 }: EventCardProps) => {
  const [isFavorited, setIsFavorited] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const handleFavorite = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setIsFavorited(!isFavorited);
    simulateHapticFeedback('light');
    toast.success(isFavorited ? 'Removed from favorites' : 'Added to favorites');
  }, [isFavorited]);

  const handleShare = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    simulateHapticFeedback('light');
    toast.success('Event shared!');
  }, []);

  const handleCardClick = useCallback(() => {
    onClick(cardRef.current);
  }, [onClick]);

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group"
    >
      <Card
        className="bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 cursor-pointer overflow-hidden text-white transition-all duration-300 group-hover:scale-[1.02]"
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleCardClick();
          }
        }}
        aria-label={`View details for ${event.title}`}
        {...createTouchHandler(handleCardClick)}
      >
        {/* Enhanced Image Section with Fallback */}
        <div className="relative">
          <FallbackImage
            src={event.image}
            alt={event.title}
            type="event"
            className="w-full h-40 sm:h-48 rounded-t-xl"
            loading="lazy"
          />

          {/* Mobile-Optimized Action Buttons Overlay */}
          <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <motion.button
              onClick={handleFavorite}
              className={`p-2 rounded-full backdrop-blur-md transition-colors ${
                isFavorited
                  ? 'bg-red-500/80 text-white'
                  : 'bg-black/40 text-white/80 hover:bg-black/60'
              }`}
              whileTap={{ scale: 0.9 }}
              style={{ touchAction: 'manipulation' }}
            >
              <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
            </motion.button>
            <motion.button
              onClick={handleShare}
              className="p-2 rounded-full bg-black/40 text-white/80 hover:bg-black/60 backdrop-blur-md transition-colors"
              whileTap={{ scale: 0.9 }}
              style={{ touchAction: 'manipulation' }}
            >
              <Share2 className="w-4 h-4" />
            </motion.button>
          </div>

          {/* Featured Badge */}
          {event.featured && (
            <div className="absolute top-2 left-2">
              <Badge className="bg-yellow-500/90 text-black font-medium">
                <Star className="w-3 h-3 mr-1 fill-current" />
                Featured
              </Badge>
            </div>
          )}
        </div>

        {/* Enhanced Content Section */}
        <CardContent className="p-3 sm:p-4">
          <div className="flex justify-between items-start mb-2">
            <CardTitle className="text-base sm:text-lg font-semibold mb-0 line-clamp-2 flex-1 mr-2">
              {event.title}
            </CardTitle>
            <Badge variant="secondary" className="bg-purple-700/50 text-white text-xs whitespace-nowrap">
              {event.category}
            </Badge>
          </div>

          <p className="text-white/70 mb-3 line-clamp-2 text-xs sm:text-sm leading-relaxed">
            {event.description}
          </p>

          {/* Enhanced Event Details */}
          <div className="space-y-1.5 mb-3">
            <div className="flex items-center text-xs sm:text-sm text-white/60">
              <Calendar className="w-3 h-3 mr-2 text-blue-400" />
              <span>{event.date}</span>
            </div>
            <div className="flex items-center text-xs sm:text-sm text-white/60">
              <MapPin className="w-3 h-3 mr-2 text-green-400" />
              <span className="truncate">{event.location}</span>
            </div>
            {event.attendees && (
              <div className="flex items-center text-xs sm:text-sm text-white/60">
                <Users className="w-3 h-3 mr-2 text-purple-400" />
                <span>{event.attendees} attending</span>
              </div>
            )}
          </div>

          {/* Mobile-Optimized Action Button */}
          <motion.button
            onClick={(e) => {
              e.stopPropagation();
              onClick(cardRef.current);
            }}
            className="w-full inline-flex items-center justify-center gap-2 touch-target px-3 py-2 bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 rounded-lg text-white font-medium text-sm transition-all duration-300"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            style={{ touchAction: 'manipulation' }}
          >
            <span>View Details</span>
            <ChevronRight className="w-3 h-3" />
          </motion.button>
        </CardContent>
      </Card>
    </motion.div>
  );
});

EventCard.displayName = 'EventCard';

// Error fallback component for Discover page
const DiscoverErrorFallback = ({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => (
  <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
    <div className="container mx-auto px-4 py-8">
      <Card className="p-6 bg-white/10 backdrop-blur-md border border-white/20 text-white">
        <CardContent className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto text-red-400 mb-4" />
          <h2 className="text-2xl font-bold mb-2">Unable to Load Events</h2>
          <p className="text-white/70 mb-6">
            We're having trouble loading the events. This might be a temporary issue.
          </p>
          <div className="bg-red-500/20 rounded-md p-4 mb-6">
            <p className="text-sm text-red-300">{error.message}</p>
          </div>
          <Button
            onClick={resetErrorBoundary}
            className="bg-purple-700 hover:bg-purple-600"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    </div>
  </div>
);

const Discover: React.FC = () => {
  // Mobile state management
  const [selectedCategory, setSelectedCategory] = useState<string>(DiscoverFilters.ALL_CATEGORIES);
  const [selectedLocation, setSelectedLocation] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Real events data state
  const [events, setEvents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Available categories and locations from real data
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableLocations, setAvailableLocations] = useState<string[]>([]);

  // Event details modal state
  const [selectedEvent, setSelectedEvent] = useState<any>(null);

  // Fetch real events from database
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        setError(null);

        const { data, error } = await supabase
          .from('events')
          .select(`
            id,
            title,
            description,
            start_date,
            end_date,
            location,
            category,
            status,
            is_active,
            created_at,
            festivals:festival_id (
              name
            )
          `)
          .eq('status', 'PUBLISHED')
          .eq('is_active', true)
          .order('start_date', { ascending: true });

        if (error) throw error;

        // Transform data to match expected format
        const transformedEvents = data?.map(event => ({
          id: event.id,
          title: event.title,
          description: event.description || 'Join this exciting event!',
          date: formatEventDate(event.start_date, event.end_date),
          location: event.location || 'Location TBD',
          category: event.category || 'OTHER',
          created_by: 'admin',
          created_at: event.created_at,
          updated_at: event.created_at,
          festival_name: event.festivals?.name || 'Festival Family Event'
        })) || [];

        setEvents(transformedEvents);

        // Extract unique categories and locations for filters
        const categories = [...new Set(transformedEvents.map(e => e.category))].filter(Boolean);
        const locations = [...new Set(transformedEvents.map(e => e.location))].filter(Boolean);

        setAvailableCategories(categories);
        setAvailableLocations(locations);

      } catch (error) {
        console.error('Error fetching events:', error);
        setError('Failed to load events');
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  // Helper function to format event dates with null safety
  const formatEventDate = (startDate: string, endDate: string) => {
    if (!startDate) return 'Date TBD';

    const start = new Date(startDate);
    if (isNaN(start.getTime())) return 'Invalid Date';

    const end = endDate ? new Date(endDate) : null;
    if (end && isNaN(end.getTime())) return 'Invalid Date';

    const formatOptions: Intl.DateTimeFormatOptions = {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    };

    if (end && start.toDateString() !== end.toDateString()) {
      return `${start.toLocaleDateString('en-US', formatOptions)} - ${end.toLocaleDateString('en-US', formatOptions)}`;
    } else {
      return start.toLocaleDateString('en-US', formatOptions);
    }
  };

  // Check if mobile viewport
  useEffect(() => {
    const checkMobile = () => setIsMobile(isMobileViewport());
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Use reduced motion hook for accessibility
  const { shouldAnimate } = useReducedMotion();

  // Handle pull-to-refresh with real data
  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    simulateHapticFeedback('medium');

    try {
      const { data, error } = await supabase
        .from('events')
        .select(`
          id,
          title,
          description,
          start_date,
          end_date,
          location,
          category,
          status,
          is_active,
          created_at,
          festivals:festival_id (
            name
          )
        `)
        .eq('status', 'PUBLISHED')
        .eq('is_active', true)
        .order('start_date', { ascending: true });

      if (error) throw error;

      // Transform data to match expected format
      const transformedEvents = data?.map(event => ({
        id: event.id,
        title: event.title,
        description: event.description || 'Join this exciting event!',
        date: formatEventDate(event.start_date, event.end_date),
        location: event.location || 'Location TBD',
        category: event.category || 'OTHER',
        created_by: 'admin',
        created_at: event.created_at,
        updated_at: event.created_at,
        festival_name: event.festivals?.name || 'Festival Family Event'
      })) || [];

      setEvents(transformedEvents);

      // Update categories and locations
      const categories = [...new Set(transformedEvents.map(e => e.category))].filter(Boolean);
      const locations = [...new Set(transformedEvents.map(e => e.location))].filter(Boolean);

      setAvailableCategories(categories);
      setAvailableLocations(locations);

      toast.success('Events refreshed!');
    } catch (error) {
      console.error('Refresh failed:', error);
      toast.error('Failed to refresh events');
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing]);

  // Memoize filtered events for better performance with real data
  const filteredEvents = useMemo(() => {
    if (loading) return [];

    return events.filter((event) => {
      const matchesCategory = selectedCategory === DiscoverFilters.ALL_CATEGORIES || event.category === selectedCategory;
      const matchesLocation = selectedLocation === 'all' || event.location === selectedLocation;
      const matchesSearch = event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.description.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesCategory && matchesLocation && matchesSearch;
    });
  }, [events, selectedCategory, selectedLocation, searchQuery, loading]);

  // State for tracking trigger element for modal positioning
  const [triggerElement, setTriggerElement] = useState<HTMLElement | null>(null);

  // Enhanced event click handler with event details modal
  const handleEventClick = useCallback((event: any, element: HTMLElement | null) => {
    simulateHapticFeedback('medium');
    setSelectedEvent(event);
    setTriggerElement(element);
    toast.success(`Opening ${event.title}...`);
  }, []);

  return (
    <ErrorBoundary FallbackComponent={DiscoverErrorFallback}>
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="container-responsive py-4 sm:py-8 text-white"
        >
          {/* Enhanced Mobile-First Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="p-4 sm:p-6 bg-white/10 backdrop-blur-md rounded-xl sm:rounded-2xl border border-white/20 text-white">
              <CardHeader className="pb-4 sm:pb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-lg flex items-center justify-center">
                      <Compass className="w-4 h-4 text-blue-400" />
                    </div>
                    <CardTitle className="text-xl sm:text-2xl font-bold">Discover Events</CardTitle>
                  </div>
                  <div className="flex items-center gap-2">
                    {/* Refresh Button */}
                    <motion.button
                      disabled={isRefreshing}
                      className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors disabled:opacity-50"
                      whileTap={{ scale: 0.95 }}
                      {...createTouchHandler(() => !isRefreshing && handleRefresh())}
                    >
                      <RefreshCw className={`w-4 h-4 text-white/70 ${isRefreshing ? 'animate-spin' : ''}`} />
                    </motion.button>

                    {/* Filter Toggle Button */}
                    <motion.button
                      className={`p-2 rounded-lg transition-colors ${
                        showFilters
                          ? 'bg-blue-600/30 text-blue-400'
                          : 'bg-white/10 hover:bg-white/20 text-white/70'
                      }`}
                      whileTap={{ scale: 0.95 }}
                      {...createTouchHandler(() => {
                        setShowFilters(!showFilters);
                        simulateHapticFeedback('light');
                      })}
                    >
                      <Filter className="w-4 h-4" />
                    </motion.button>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                {/* Mobile-Optimized Search Bar */}
                <div className="mb-4 sm:mb-6">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" />
                    <Input
                      type="text"
                      placeholder={isMobile ? "Search events..." : "Search festival events..."}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-blue-400 text-mobile-base rounded-xl"
                      style={{ fontSize: '16px' }} // Prevents zoom on iOS
                    />
                  </div>
                </div>

                {/* Enhanced Mobile Filters Panel */}
                <AnimatePresence>
                  {showFilters && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10 mb-4 sm:mb-6"
                    >
                      <h4 className="text-sm font-medium text-white/80 mb-3">Filters</h4>
                      <div className="space-y-3">
                        {/* Category Filter */}
                        <div>
                          <label className="block text-xs font-medium text-white/60 mb-2">Category</label>
                          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                            <SelectTrigger
                              className="w-full min-h-[44px] bg-white/10 border-white/20 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-lg"
                              aria-label="Filter by category"
                            >
                              <SelectValue placeholder="Category" />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-white/20 text-white">
                              <SelectItem value={DiscoverFilters.ALL_CATEGORIES}>All Categories</SelectItem>
                              {availableCategories.map((category) => (
                                <SelectItem key={category} value={category}>{category}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Location Filter */}
                        <div>
                          <label className="block text-xs font-medium text-white/60 mb-2">Location</label>
                          <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                            <SelectTrigger
                              className="w-full min-h-[44px] bg-white/10 border-white/20 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-lg"
                              aria-label="Filter by location"
                            >
                              <SelectValue placeholder="Location" />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-white/20 text-white">
                              <SelectItem value="all">All Locations</SelectItem>
                              {availableLocations.map((location) => (
                                <SelectItem key={location} value={location}>{location}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Enhanced Events Grid */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  {loading ? (
                    /* Loading State */
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                      {[1, 2, 3, 4].map((i) => (
                        <div key={i} className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-4 animate-pulse">
                          <div className="h-4 bg-white/20 rounded mb-2"></div>
                          <div className="h-3 bg-white/10 rounded mb-4"></div>
                          <div className="h-8 bg-white/10 rounded"></div>
                        </div>
                      ))}
                    </div>
                  ) : error ? (
                    /* Error State */
                    <div className="text-center py-12 sm:py-16">
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5 }}
                        className="w-20 h-20 bg-red-500/10 rounded-full flex items-center justify-center mx-auto mb-6"
                      >
                        <AlertCircle className="w-10 h-10 text-red-400" />
                      </motion.div>
                      <h3 className="text-lg sm:text-xl font-medium text-white/60 mb-2">Failed to load events</h3>
                      <p className="text-white/40 text-sm sm:text-base mb-6 max-w-md mx-auto">
                        {error}
                      </p>
                      <motion.button
                        onClick={handleRefresh}
                        disabled={isRefreshing}
                        className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white font-medium text-sm transition-all duration-300 disabled:opacity-50"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                        Try Again
                      </motion.button>
                    </div>
                  ) : filteredEvents.length > 0 ? (
                    <>
                      {/* Results Summary */}
                      <div className="flex items-center justify-between mb-4">
                        <p className="text-white/70 text-sm">
                          {filteredEvents.length} event{filteredEvents.length !== 1 ? 's' : ''} found
                        </p>
                        {searchQuery && (
                          <Badge variant="outline" className="border-white/20 text-white/60 text-xs">
                            "{searchQuery}"
                          </Badge>
                        )}
                      </div>

                      {/* Mobile-Optimized Events Grid */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                        {filteredEvents.map((event, index) => (
                          <EventCard
                            key={event.id}
                            event={event}
                            index={index}
                            onClick={(element) => handleEventClick(event, element)}
                          />
                        ))}
                      </div>
                    </>
                  ) : (
                    /* Enhanced Empty State */
                    <div className="text-center py-12 sm:py-16">
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5 }}
                        className="w-20 h-20 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-6"
                      >
                        <Compass className="w-10 h-10 text-white/40" />
                      </motion.div>
                      <h3 className="text-lg sm:text-xl font-medium text-white/60 mb-2">No events found</h3>
                      <p className="text-white/40 text-sm sm:text-base mb-6 max-w-md mx-auto">
                        {searchQuery
                          ? `No events match "${searchQuery}". Try adjusting your search terms or filters.`
                          : 'Try adjusting your filters to discover amazing events.'
                        }
                      </p>
                      {(searchQuery || selectedCategory !== DiscoverFilters.ALL_CATEGORIES || selectedLocation !== 'all') && (
                        <motion.button
                          onClick={() => {
                            setSearchQuery('');
                            setSelectedCategory(DiscoverFilters.ALL_CATEGORIES);
                            setSelectedLocation('all');
                            simulateHapticFeedback('light');
                            toast.success('Filters cleared');
                          }}
                          className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white font-medium text-sm transition-all duration-300"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          Clear Filters
                        </motion.button>
                      )}
                    </div>
                  )}
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Mobile-safe bottom spacing */}
          <div className="h-4 sm:h-8" />
        </motion.div>

        {/* Development-only Mobile UX Testing Tool */}
        <MobileUXTester />
      </div>

      {/* Enhanced Event Details Modal with ResponsiveModal System */}
      <EventDetailsModal
        event={selectedEvent}
        isOpen={!!selectedEvent}
        onClose={() => {
          setSelectedEvent(null);
          setTriggerElement(null);
        }}
        triggerElement={triggerElement}
      />
    </ErrorBoundary>
  );
};

export default Discover;
