import React, { useEffect, useState, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../providers/ConsolidatedAuthProvider';
import { checkSupabaseConnection } from '@/lib/supabase';
import { toast } from 'react-hot-toast';
import { Calendar, Users, Music, Star, TrendingUp, Bell, RefreshCw, ArrowRight, Heart } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { simulateHapticFeedback, isMobileViewport, createTouchHandler } from '../utils/mobileUX';
import MobileUXTester from '../components/testing/MobileUXTester';

/**
 * Authenticated Home Page
 * 
 * This is the main dashboard for logged-in users, providing personalized
 * content, quick access to key features, and user-specific information.
 */
const AuthenticatedHome: React.FC = () => {
  const { user, profile, loading, refreshProfile } = useAuth();

  // Mobile-specific state management
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Check if mobile viewport
  useEffect(() => {
    const checkMobile = () => setIsMobile(isMobileViewport());
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Pull-to-refresh implementation
  const handlePullToRefresh = useCallback(async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    simulateHapticFeedback('medium');

    try {
      // Refresh profile and check connection
      await Promise.all([
        refreshProfile(),
        checkSupabaseConnection()
      ]);

      setLastRefresh(new Date());
      toast.success('Dashboard refreshed!');
    } catch (error) {
      console.error('Refresh failed:', error);
      toast.error('Failed to refresh. Please try again.');
    } finally {
      setIsRefreshing(false);
      setPullDistance(0);
      setIsPulling(false);
    }
  }, [isRefreshing, refreshProfile]);

  // Touch event handlers for pull-to-refresh
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (window.scrollY === 0 && isMobile) {
      setIsPulling(true);
    }
  }, [isMobile]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isPulling || !isMobile) return;

    const touch = e.touches[0];
    const startY = touch.clientY;
    const distance = Math.max(0, Math.min(startY / 3, 100)); // Max 100px pull
    setPullDistance(distance);
  }, [isPulling, isMobile]);

  const handleTouchEnd = useCallback(() => {
    if (!isPulling || !isMobile) return;

    if (pullDistance > 60) { // Threshold for refresh
      handlePullToRefresh();
    } else {
      setPullDistance(0);
      setIsPulling(false);
    }
  }, [isPulling, isMobile, pullDistance, handlePullToRefresh]);

  useEffect(() => {
    // Check Supabase connection when component mounts (silent check - no toast)
    checkSupabaseConnection().then(connectionResult => {
      if (!connectionResult.success) {
        console.error('Connection failed:', connectionResult.message);
        // Remove duplicate toast - connection status is handled by ConnectionStatus component
      } else if (user && !profile) {
        // If we have a user but no profile, try to refresh the profile
        console.log('User exists but no profile found. Attempting to refresh profile...');
        refreshProfile();
      }
    });
  }, [user, profile, refreshProfile]);

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center container-responsive">
        <motion.div
          className="text-center text-white"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="w-12 h-12 sm:w-16 sm:h-16 border-3 border-white/30 border-t-purple-400 rounded-full mx-auto mb-4"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          <h2 className="text-lg sm:text-xl font-semibold mb-2">Loading your dashboard...</h2>
          <p className="text-sm sm:text-base text-white/70">
            {isMobile ? 'Pull down to refresh when ready' : 'If this takes too long, try refreshing the page.'}
          </p>
        </motion.div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex-1 flex items-center justify-center container-responsive">
        <motion.div
          className="text-center text-white"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="w-16 h-16 sm:w-20 sm:h-20 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <Users className="w-8 h-8 sm:w-10 sm:h-10 text-purple-400" />
          </div>
          <h2 className="text-lg sm:text-xl font-semibold mb-4">Please sign in to access your dashboard</h2>
          {/* Authentication handled by navigation components - no duplicate sign-in button needed */}
        </motion.div>
      </div>
    );
  }

  return (
    <div
      className="flex-1 relative overflow-hidden"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Pull-to-Refresh Indicator */}
      <AnimatePresence>
        {(isPulling || isRefreshing) && (
          <motion.div
            className="absolute top-0 left-0 right-0 z-50 flex items-center justify-center bg-gradient-to-b from-purple-600/20 to-transparent backdrop-blur-sm"
            initial={{ height: 0, opacity: 0 }}
            animate={{
              height: Math.max(60, pullDistance + 20),
              opacity: 1
            }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <motion.div
              className="flex items-center gap-2 text-white"
              animate={isRefreshing ? { rotate: 360 } : { rotate: pullDistance * 3.6 }}
              transition={isRefreshing ? { duration: 1, repeat: Infinity, ease: "linear" } : { duration: 0.1 }}
            >
              <RefreshCw className="w-5 h-5" />
              <span className="text-sm font-medium">
                {isRefreshing ? 'Refreshing...' : pullDistance > 60 ? 'Release to refresh' : 'Pull to refresh'}
              </span>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content Container - Mobile Optimized with Festival Family 2025 Design */}
      <div className="container-mobile-optimized mobile-spacing-normal mobile-content-spacing bg-gradient-sunset min-h-screen">
        {/* Enhanced Mobile-First Welcome Header */}
        <motion.div
          className="bg-neutral-50/95 backdrop-blur-md rounded-xl sm:rounded-2xl border border-neutral-200/50 shadow-lg mobile-padding-comfortable"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* User Info - Mobile Optimized */}
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3 sm:mb-2">
                {/* Mobile Avatar */}
                <div className="sm:hidden">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt="Profile"
                      className="w-12 h-12 rounded-full border-2 border-festival-orange"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gradient-to-br from-festival-orange/20 to-festival-pink/20 rounded-full flex items-center justify-center border-2 border-festival-orange">
                      <Users className="w-6 h-6 text-festival-orange" />
                    </div>
                  )}
                </div>

                {/* Welcome Text */}
                <div className="flex-1">
                  <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-neutral-800 leading-tight">
                    Welcome back,
                    <br className="sm:hidden" />
                    <span className="sm:hidden"> </span>
                    <span className="text-festival-gradient">
                      {profile?.full_name ?? user.email?.split('@')[0] ?? 'Festival Goer'}
                    </span>!
                  </h1>
                </div>
              </div>

              <p className="text-sm sm:text-base text-neutral-700 leading-relaxed font-medium">
                Ready to discover your next festival adventure?
              </p>

              {/* Last Refresh Indicator */}
              {lastRefresh && (
                <p className="text-xs text-neutral-500 mt-2">
                  Last updated: {lastRefresh.toLocaleTimeString()}
                </p>
              )}
            </div>

            {/* Desktop Avatar & Stats */}
            <div className="hidden sm:flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm text-neutral-600 font-medium">Member since</p>
                <p className="text-neutral-800 font-semibold">
                  {profile?.created_at ? new Date(profile.created_at).toLocaleDateString() : 'Recently'}
                </p>
              </div>
              {profile?.avatar_url ? (
                <img
                  src={profile.avatar_url}
                  alt="Profile"
                  className="w-12 h-12 rounded-full border-2 border-festival-orange shadow-md"
                />
              ) : (
                <div className="w-12 h-12 bg-gradient-to-br from-festival-orange/20 to-festival-pink/20 rounded-full flex items-center justify-center border-2 border-festival-orange shadow-md">
                  <Users className="w-6 h-6 text-festival-orange" />
                </div>
              )}
            </div>
          </div>
        </motion.div>

        {/* Enhanced Mobile-First Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg sm:text-xl font-semibold text-neutral-800">Quick Actions</h2>
            <motion.button
              disabled={isRefreshing}
              className="btn-icon disabled:opacity-50"
              whileTap={{ scale: 0.95 }}
              {...createTouchHandler(() => !isRefreshing && handlePullToRefresh())}
              title="Pull to refresh"
              aria-label="Pull to refresh"
            >
              <RefreshCw className={`w-4 h-4 text-neutral-600 ${isRefreshing ? 'animate-spin' : ''}`} />
            </motion.button>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            {/* Activities Card */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                to="/activities"
                className="block h-full mobile-padding-comfortable bg-neutral-50/90 backdrop-blur-md rounded-xl sm:rounded-2xl border border-neutral-200/50 hover:bg-neutral-100/90 hover:border-neutral-300/50 hover:shadow-md transition-all duration-300 group touch-target"
                style={{ touchAction: 'manipulation' }}
              >
                <div className="flex flex-col items-center text-center gap-2 sm:gap-3">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-festival-orange/20 to-festival-orange/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Calendar className="w-6 h-6 sm:w-7 sm:h-7 text-festival-orange" />
                  </div>
                  <div>
                    <h3 className="text-sm sm:text-lg font-semibold text-neutral-800 mb-1">Activities</h3>
                    <p className="text-xs sm:text-sm text-neutral-600 leading-relaxed">
                      Browse and join festival activities
                    </p>
                  </div>
                </div>
              </Link>
            </motion.div>

            {/* FamHub Card */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                to="/famhub"
                className="block h-full mobile-padding-comfortable bg-neutral-50/90 backdrop-blur-md rounded-xl sm:rounded-2xl border border-neutral-200/50 hover:bg-neutral-100/90 hover:border-neutral-300/50 hover:shadow-md transition-all duration-300 group touch-target"
                style={{ touchAction: 'manipulation' }}
              >
                <div className="flex flex-col items-center text-center gap-2 sm:gap-3">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-festival-purple/20 to-festival-purple/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Users className="w-6 h-6 sm:w-7 sm:h-7 text-festival-purple" />
                  </div>
                  <div>
                    <h3 className="text-sm sm:text-lg font-semibold text-neutral-800 mb-1">FamHub</h3>
                    <p className="text-xs sm:text-sm text-neutral-600 leading-relaxed">
                      Connect with your festival family
                    </p>
                  </div>
                </div>
              </Link>
            </motion.div>

            {/* Discover Card */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                to="/discover"
                className="block h-full mobile-padding-comfortable bg-neutral-50/90 backdrop-blur-md rounded-xl sm:rounded-2xl border border-neutral-200/50 hover:bg-neutral-100/90 hover:border-neutral-300/50 hover:shadow-md transition-all duration-300 group touch-target"
                style={{ touchAction: 'manipulation' }}
              >
                <div className="flex flex-col items-center text-center gap-2 sm:gap-3">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-festival-teal/20 to-festival-teal/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Music className="w-6 h-6 sm:w-7 sm:h-7 text-festival-teal" />
                  </div>
                  <div>
                    <h3 className="text-sm sm:text-lg font-semibold text-neutral-800 mb-1">Discover</h3>
                    <p className="text-xs sm:text-sm text-neutral-600 leading-relaxed">
                      Find new festivals and events
                    </p>
                  </div>
                </div>
              </Link>
            </motion.div>

            {/* Profile Card */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                to="/profile"
                className="block h-full mobile-padding-comfortable bg-neutral-50/90 backdrop-blur-md rounded-xl sm:rounded-2xl border border-neutral-200/50 hover:bg-neutral-100/90 hover:border-neutral-300/50 hover:shadow-md transition-all duration-300 group touch-target"
                style={{ touchAction: 'manipulation' }}
              >
                <div className="flex flex-col items-center text-center gap-2 sm:gap-3">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-festival-pink/20 to-festival-pink/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Star className="w-6 h-6 sm:w-7 sm:h-7 text-festival-pink" />
                  </div>
                  <div>
                    <h3 className="text-sm sm:text-lg font-semibold text-neutral-800 mb-1">Profile</h3>
                    <p className="text-xs sm:text-sm text-neutral-600 leading-relaxed">
                      Manage your profile and settings
                    </p>
                  </div>
                </div>
              </Link>
            </motion.div>
          </div>
        </motion.div>

        {/* Enhanced Mobile-First Dashboard Content */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {/* Recent Activity - Mobile Optimized */}
          <motion.div
            className="lg:col-span-2 bg-neutral-50/95 backdrop-blur-md rounded-xl sm:rounded-2xl border border-neutral-200/50 shadow-lg mobile-padding-comfortable"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-festival-orange/20 to-festival-orange/30 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 text-festival-orange" />
                </div>
                <h2 className="text-lg sm:text-xl font-semibold text-neutral-800">Recent Activity</h2>
              </div>
              <motion.button
                className="btn-icon"
                whileTap={{ scale: 0.95 }}
                {...createTouchHandler(() => {
                  // TODO: Implement activity refresh functionality
                  toast.success('Activity refresh feature coming soon!');
                })}
                title="Refresh activity feed"
                aria-label="Refresh activity feed"
              >
                <RefreshCw className="w-4 h-4 text-neutral-600" />
              </motion.button>
            </div>

            <div className="space-y-3 sm:space-y-4">
              <motion.div
                className="p-3 sm:p-4 bg-neutral-100/80 rounded-lg sm:rounded-xl border border-neutral-200/50"
                whileHover={{ scale: 1.01 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-festival-teal/20 to-festival-teal/30 rounded-full flex items-center justify-center">
                    <Heart className="w-4 h-4 text-festival-teal" />
                  </div>
                  <div className="flex-1">
                    <p className="text-neutral-700 text-sm sm:text-base font-medium">No recent activity yet.</p>
                    <p className="text-neutral-500 text-xs sm:text-sm mt-1">
                      Start exploring to see your activity here!
                    </p>
                  </div>
                </div>
                <div className="flex gap-2 mt-3">
                  <Link
                    to="/activities"
                    className="btn-action-sm"
                  >
                    Browse Activities
                  </Link>
                  <Link
                    to="/discover"
                    className="btn-action-sm bg-pink-600/20 hover:bg-pink-600/30 text-pink-300"
                  >
                    Discover Events
                  </Link>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Notifications & Updates - Mobile Optimized */}
          <motion.div
            className="bg-white/10 backdrop-blur-md rounded-xl sm:rounded-2xl border border-white/20 mobile-padding-comfortable"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <div className="flex items-center gap-3 mb-4 sm:mb-6">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-lg flex items-center justify-center">
                <Bell className="w-4 h-4 sm:w-5 sm:h-5 text-blue-400" />
              </div>
              <h2 className="text-lg sm:text-xl font-semibold text-white">Updates</h2>
            </div>

            <div className="space-y-3 sm:space-y-4">
              <motion.div
                className="p-3 sm:p-4 bg-white/5 rounded-lg sm:rounded-xl border border-white/10"
                whileHover={{ scale: 1.01 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <Star className="w-4 h-4 text-green-400" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-white font-medium text-sm sm:text-base mb-2">Welcome to Festival Family!</h3>
                    <p className="text-white/70 text-xs sm:text-sm leading-relaxed mb-3">
                      Complete your profile to start connecting with other festival-goers and unlock all features.
                    </p>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Link
                        to="/profile"
                        className="inline-flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-purple-600/20 to-pink-600/20 hover:from-purple-600/30 hover:to-pink-600/30 rounded-lg text-purple-300 text-xs sm:text-sm font-medium transition-all duration-300 touch-target"
                        style={{ touchAction: 'manipulation' }}
                      >
                        <span>Complete Profile</span>
                        <ArrowRight className="w-3 h-3" />
                      </Link>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>

        {/* Enhanced Error State - Mobile Optimized */}
        {user && !loading && !profile && (
          <motion.div
            className="bg-yellow-500/10 backdrop-blur-md rounded-xl sm:rounded-2xl border border-yellow-500/20 p-4 sm:p-6"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <div className="text-center">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-yellow-500/20 to-yellow-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Bell className="w-6 h-6 sm:w-8 sm:h-8 text-yellow-400" />
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-yellow-300 mb-2">Profile Loading Issue</h3>
              <p className="text-yellow-200/80 text-sm sm:text-base mb-6 leading-relaxed">
                We're having trouble loading your profile data. This might be due to a connection issue.
              </p>
              <motion.button
                type="button"
                onClick={() => {
                  refreshProfile();
                  toast.success('Attempting to refresh your profile...');
                }}
                className="w-full sm:w-auto px-6 py-3 bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-500 hover:to-yellow-400 rounded-xl text-white font-medium transition-all duration-300 touch-target"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                style={{ touchAction: 'manipulation' }}
              >
                Retry Loading Profile
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Mobile-safe bottom spacing */}
        <div className="h-4 sm:h-8" />
      </div>

      {/* Development-only Mobile UX Testing Tool */}
      <MobileUXTester />
    </div>
  );
};

export default AuthenticatedHome;
