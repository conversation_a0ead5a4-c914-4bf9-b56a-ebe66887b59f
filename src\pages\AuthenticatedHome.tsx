import React, { useEffect, useState, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../providers/ConsolidatedAuthProvider';
import { checkSupabaseConnection, supabase } from '@/lib/supabase';
import { toast } from 'react-hot-toast';
import { Calendar, Users, Music, Star, TrendingUp, Bell, RefreshCw, ArrowRight, Heart, Activity, MapPin, Clock, Eye } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { simulateHapticFeedback, isMobileViewport, createTouchHandler } from '../utils/mobileUX';
import MobileUXTester from '../components/testing/MobileUXTester';
import AnnouncementPopup from '../components/announcements/AnnouncementPopup';
import { useAnnouncementDismissal } from '@/hooks/useAnnouncementDismissal';
import type { Announcement } from '@/types';
import type { Database } from '@/types/supabase';
import { AnnouncementDisplayType, AnnouncementPriority } from '@/types/announcements';
// Import new unified design system components
import {
  PageWrapper,
  UnifiedCard,
  UnifiedButton,
  UnifiedBadge,
  UnifiedAnnouncement,
  GridLayout,
  FlexLayout
} from '@/components/design-system';

// Types for dashboard data (merged from Home.tsx)
type AnnouncementRow = Database['public']['Tables']['announcements']['Row'] & {
  category?: Array<{
    id: string;
    name: string;
    color: string | null;
    description: string | null;
    created_at: string;
    updated_at: string;
  }>;
};

interface Festival {
  id: string;
  name: string;
  description: string | null;
  start_date?: string;
  end_date?: string;
  location?: string;
  featured?: boolean;
}

interface Activity {
  id: string;
  title: string;
  description: string;
  type: string;
  location?: string | null;
  start_date?: string | null;
  end_date?: string | null;
  capacity?: number | null;
  is_featured?: boolean | null;
  festival_id?: string | null;
}

interface Tip {
  id: string;
  title: string;
  description?: string | null;
  category: string | null;
  is_featured?: boolean | null;
  helpful_count?: number | null;
  view_count?: number | null;
}

interface Guide {
  id: string;
  title: string;
  description?: string | null;
  category: string | null;
  is_featured?: boolean | null;
  view_count?: number | null;
}

/**
 * Enhanced Authenticated Home Page
 *
 * Comprehensive dashboard for logged-in users with:
 * - Personalized content and quick access to features
 * - Today's activities, featured tips, and guides
 * - Recent announcements with priority-based color coding
 * - Pull-to-refresh functionality for mobile
 * - Uses unified design system to eliminate background layering
 */
const AuthenticatedHome: React.FC = () => {
  const { user, profile, loading, refreshProfile } = useAuth();

  // Mobile-specific state management
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Dashboard data state (merged from Home.tsx)
  const [showAnnouncement, setShowAnnouncement] = useState(false);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [todaysActivities, setTodaysActivities] = useState<Activity[]>([]);
  const [featuredTips, setFeaturedTips] = useState<Tip[]>([]);
  const [featuredGuides, setFeaturedGuides] = useState<Guide[]>([]);
  const [recentAnnouncements, setRecentAnnouncements] = useState<AnnouncementRow[]>([]);
  const [dashboardLoading, setDashboardLoading] = useState(true);

  // Announcement dismissal
  const {
    dismissAnnouncement,
    isDismissed
  } = useAnnouncementDismissal();

  // Get today's date for filtering
  const today = new Date().toISOString().split('T')[0];

  // Fetch dashboard data (merged from Home.tsx)
  const fetchDashboardData = async () => {
    try {
      setDashboardLoading(true);

      // Fetch today's activities
      const { data: activities } = await supabase
        .from('activities')
        .select('*')
        .gte('start_date', today)
        .lte('start_date', `${today}T23:59:59`)
        .order('start_date', { ascending: true })
        .limit(6);

      // Fetch featured tips
      const { data: tips } = await supabase
        .from('tips')
        .select('*')
        .eq('is_featured', true)
        .eq('status', 'published')
        .order('helpful_count', { ascending: false })
        .limit(4);

      // Fetch featured guides
      const { data: guides } = await supabase
        .from('guides')
        .select('*')
        .eq('is_featured', true)
        .eq('status', 'published')
        .order('view_count', { ascending: false })
        .limit(4);

      // Fetch recent announcements
      const { data: announcementsData } = await supabase
        .from('announcements')
        .select('*')
        .eq('active', true)
        .order('created_at', { ascending: false })
        .limit(3);

      setTodaysActivities(activities || []);
      setFeaturedTips(tips || []);
      setFeaturedGuides(guides || []);
      setRecentAnnouncements(announcementsData || []);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setDashboardLoading(false);
    }
  };

  // Fetch popup announcements
  const fetchAnnouncements = async () => {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .eq('active', true)
        .eq('display_type', 'popup')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;

      const mappedAnnouncements: Announcement[] = (data || []).map(row => ({
        ...row,
        priority: (row.priority as AnnouncementPriority) || 'NORMAL',
        target_audience: row.target_audience as string[] || [],
        active: row.active || false,
        display_type: row.display_type as AnnouncementDisplayType,
      }));

      const activeAnnouncements = mappedAnnouncements.filter(ann =>
        ann.active && !isDismissed(ann.id.toString())
      );

      setAnnouncements(activeAnnouncements);

      if (activeAnnouncements.length > 0) {
        setShowAnnouncement(true);
      }
    } catch (error) {
      console.error('Error fetching announcements:', error);
    }
  };

  // Check if mobile viewport
  useEffect(() => {
    const checkMobile = () => setIsMobile(isMobileViewport());
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Enhanced pull-to-refresh implementation
  const handlePullToRefresh = useCallback(async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    simulateHapticFeedback('medium');

    try {
      // Refresh profile, check connection, and fetch dashboard data
      await Promise.all([
        refreshProfile(),
        checkSupabaseConnection(),
        fetchDashboardData(),
        fetchAnnouncements()
      ]);

      setLastRefresh(new Date());
      toast.success('Dashboard refreshed!');
    } catch (error) {
      console.error('Refresh failed:', error);
      toast.error('Failed to refresh. Please try again.');
    } finally {
      setIsRefreshing(false);
      setPullDistance(0);
      setIsPulling(false);
    }
  }, [isRefreshing, refreshProfile]);

  // Touch event handlers for pull-to-refresh
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (window.scrollY === 0 && isMobile) {
      setIsPulling(true);
    }
  }, [isMobile]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isPulling || !isMobile) return;

    const touch = e.touches[0];
    const startY = touch.clientY;
    const distance = Math.max(0, Math.min(startY / 3, 100)); // Max 100px pull
    setPullDistance(distance);
  }, [isPulling, isMobile]);

  const handleTouchEnd = useCallback(() => {
    if (!isPulling || !isMobile) return;

    if (pullDistance > 60) { // Threshold for refresh
      handlePullToRefresh();
    } else {
      setPullDistance(0);
      setIsPulling(false);
    }
  }, [isPulling, isMobile, pullDistance, handlePullToRefresh]);

  // Utility functions for formatting
  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getPriorityLevel = (priority?: string): 'high' | 'medium' | 'low' => {
    switch (priority?.toLowerCase()) {
      case 'urgent':
      case 'high':
        return 'high';
      case 'medium':
      case 'normal':
        return 'medium';
      default:
        return 'low';
    }
  };

  useEffect(() => {
    // Check Supabase connection and fetch data when component mounts
    checkSupabaseConnection().then(connectionResult => {
      if (!connectionResult.success) {
        console.error('Connection failed:', connectionResult.message);
        // Remove duplicate toast - connection status is handled by ConnectionStatus component
      } else if (user && !profile) {
        // If we have a user but no profile, try to refresh the profile
        console.log('User exists but no profile found. Attempting to refresh profile...');
        refreshProfile();
      }
    });

    // Fetch dashboard data
    fetchDashboardData();
    fetchAnnouncements();
  }, [user, profile, refreshProfile]);

  if (loading || dashboardLoading) {
    return (
      <PageWrapper title="Loading..." variant="default">
        <div className="flex items-center justify-center min-h-96">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              className="w-12 h-12 sm:w-16 sm:h-16 border-3 border-white/30 border-t-purple-400 rounded-full mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <h2 className="text-lg sm:text-xl font-semibold mb-2 text-primary-dark">Loading your dashboard...</h2>
            <p className="text-sm sm:text-base text-secondary-dark">
              {isMobile ? 'Pull down to refresh when ready' : 'If this takes too long, try refreshing the page.'}
            </p>
          </motion.div>
        </div>
      </PageWrapper>
    );
  }

  if (!user) {
    return (
      <PageWrapper title="Welcome to Festival Family" variant="default">
        <div className="flex items-center justify-center min-h-96">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Users className="w-8 h-8 sm:w-10 sm:h-10 text-purple-400" />
            </div>
            <h2 className="text-lg sm:text-xl font-semibold mb-4 text-primary-dark">Please sign in to access your dashboard</h2>
            {/* Authentication handled by navigation components - no duplicate sign-in button needed */}
          </motion.div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <>
      {/* Announcement Popup */}
      <AnimatePresence>
        {showAnnouncement && announcements.length > 0 && (
          <AnnouncementPopup
            announcement={announcements[currentIndex]}
            onClose={() => {
              dismissAnnouncement(announcements[currentIndex].id.toString());
              if (currentIndex < announcements.length - 1) {
                setCurrentIndex(currentIndex + 1);
              } else {
                setShowAnnouncement(false);
              }
            }}
            onNext={announcements.length > 1 ? () => {
              if (currentIndex < announcements.length - 1) {
                setCurrentIndex(currentIndex + 1);
              } else {
                setCurrentIndex(0);
              }
            } : undefined}
            onPrevious={announcements.length > 1 ? () => {
              if (currentIndex > 0) {
                setCurrentIndex(currentIndex - 1);
              } else {
                setCurrentIndex(announcements.length - 1);
              }
            } : undefined}
            currentIndex={currentIndex}
            totalCount={announcements.length}
          />
        )}
      </AnimatePresence>

      <div
        className="flex-1 relative overflow-hidden"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Pull-to-Refresh Indicator */}
        <AnimatePresence>
          {(isPulling || isRefreshing) && (
            <motion.div
              className="absolute top-0 left-0 right-0 z-50 flex items-center justify-center bg-gradient-to-b from-purple-600/20 to-transparent backdrop-blur-sm"
              initial={{ height: 0, opacity: 0 }}
              animate={{
                height: Math.max(60, pullDistance + 20),
                opacity: 1
              }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <motion.div
                className="flex items-center gap-2 text-white"
                animate={isRefreshing ? { rotate: 360 } : { rotate: pullDistance * 3.6 }}
                transition={isRefreshing ? { duration: 1, repeat: Infinity, ease: "linear" } : { duration: 0.1 }}
              >
                <RefreshCw className="w-5 h-5" />
                <span className="text-sm font-medium">
                  {isRefreshing ? 'Refreshing...' : pullDistance > 60 ? 'Release to refresh' : 'Pull to refresh'}
                </span>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Dashboard Content using Unified Design System */}
        <PageWrapper
          title={`Welcome back, ${profile?.full_name ?? user.email?.split('@')[0] ?? 'Festival Goer'}!`}
          subtitle="Ready to discover your next festival adventure?"
          actions={
            <UnifiedButton
              variant="ghost"
              size="sm"
              onClick={handlePullToRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </UnifiedButton>
          }
        >
          {/* Quick Stats Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <GridLayout cols={3} gap="md" className="mb-8">
              <UnifiedCard variant="elevated">
                <div className="p-6 text-center">
                  <Activity className="w-10 h-10 mx-auto mb-3 text-festival-orange" />
                  <div className="text-3xl font-bold text-primary-light mb-1">{todaysActivities.length}</div>
                  <div className="text-sm text-secondary-light font-medium">Today's Activities</div>
                </div>
              </UnifiedCard>
              <UnifiedCard variant="elevated">
                <div className="p-6 text-center">
                  <Star className="w-10 h-10 mx-auto mb-3 text-festival-orange" />
                  <div className="text-3xl font-bold text-primary-light mb-1">{featuredTips.length}</div>
                  <div className="text-sm text-secondary-light font-medium">Featured Tips</div>
                </div>
              </UnifiedCard>
              <UnifiedCard variant="elevated">
                <div className="p-6 text-center">
                  <Bell className="w-10 h-10 mx-auto mb-3 text-festival-orange" />
                  <div className="text-3xl font-bold text-primary-light mb-1">{recentAnnouncements.length}</div>
                  <div className="text-sm text-secondary-light font-medium">New Announcements</div>
                </div>
              </UnifiedCard>
            </GridLayout>
          </motion.div>

          {/* Recent Announcements with Priority-Based Color Coding */}
          {recentAnnouncements.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="mb-8"
            >
              <h2 className="text-2xl font-bold text-primary-light mb-6">Recent Announcements</h2>
              <div className="space-y-4">
                {recentAnnouncements.slice(0, 3).map((announcement) => (
                  <UnifiedAnnouncement
                    key={announcement.id}
                    priority={getPriorityLevel(announcement.priority)}
                    title={announcement.title}
                    message={announcement.message || ''}
                  />
                ))}
              </div>
            </motion.div>
          )}

          {/* Today's Activities */}
          {todaysActivities.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <FlexLayout justify="between" align="center" className="mb-6">
                <h2 className="text-2xl font-bold text-primary-light">Today's Activities</h2>
                <Link to="/activities">
                  <UnifiedButton variant="outline" size="sm">
                    View All
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </UnifiedButton>
                </Link>
              </FlexLayout>
              <GridLayout cols={2} gap="md">
                {todaysActivities.slice(0, 4).map((activity) => (
                  <UnifiedCard key={activity.id} variant="default" className="p-4">
                    <FlexLayout direction="col" gap="sm">
                      <FlexLayout justify="between" align="start">
                        <h3 className="font-semibold text-primary-light">{activity.title}</h3>
                        <UnifiedBadge variant="category">{activity.type}</UnifiedBadge>
                      </FlexLayout>
                      <p className="text-sm text-secondary-light line-clamp-2">{activity.description}</p>
                      <FlexLayout align="center" gap="sm" className="text-xs text-muted-light">
                        {activity.start_date && (
                          <FlexLayout align="center" gap="sm">
                            <Clock className="w-3 h-3" />
                            <span>{formatTime(activity.start_date)}</span>
                          </FlexLayout>
                        )}
                        {activity.location && (
                          <FlexLayout align="center" gap="sm">
                            <MapPin className="w-3 h-3" />
                            <span>{activity.location}</span>
                          </FlexLayout>
                        )}
                      </FlexLayout>
                    </FlexLayout>
                  </UnifiedCard>
                ))}
              </GridLayout>
            </motion.div>
          )}

          {/* Featured Tips and Guides */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-8"
          >
            <GridLayout cols={2} gap="lg">
              {/* Featured Tips */}
              <div>
                <FlexLayout justify="between" align="center" className="mb-4">
                  <h2 className="text-xl font-bold text-primary-light">Featured Tips</h2>
                  <Link to="/famhub?tab=RESOURCES">
                    <UnifiedButton variant="ghost" size="sm">
                      View All
                    </UnifiedButton>
                  </Link>
                </FlexLayout>
                <div className="space-y-3">
                  {featuredTips.slice(0, 3).map((tip) => (
                    <UnifiedCard key={tip.id} variant="outlined" className="p-4">
                      <FlexLayout direction="col" gap="sm">
                        <h3 className="font-medium text-primary-light">{tip.title}</h3>
                        {tip.description && (
                          <p className="text-sm text-secondary-light line-clamp-2">{tip.description}</p>
                        )}
                        <FlexLayout align="center" gap="sm" className="text-xs text-muted-light">
                          {tip.helpful_count && (
                            <FlexLayout align="center" gap="sm">
                              <Heart className="w-3 h-3" />
                              <span>{tip.helpful_count} helpful</span>
                            </FlexLayout>
                          )}
                          {tip.view_count && (
                            <FlexLayout align="center" gap="sm">
                              <Eye className="w-3 h-3" />
                              <span>{tip.view_count} views</span>
                            </FlexLayout>
                          )}
                        </FlexLayout>
                      </FlexLayout>
                    </UnifiedCard>
                  ))}
                </div>
              </div>

              {/* Featured Guides */}
              <div>
                <FlexLayout justify="between" align="center" className="mb-4">
                  <h2 className="text-xl font-bold text-primary-light">Featured Guides</h2>
                  <Link to="/famhub?tab=RESOURCES">
                    <UnifiedButton variant="ghost" size="sm">
                      View All
                    </UnifiedButton>
                  </Link>
                </FlexLayout>
                <div className="space-y-3">
                  {featuredGuides.slice(0, 3).map((guide) => (
                    <UnifiedCard key={guide.id} variant="outlined" className="p-4">
                      <FlexLayout direction="col" gap="sm">
                        <h3 className="font-medium text-primary-light">{guide.title}</h3>
                        {guide.description && (
                          <p className="text-sm text-secondary-light line-clamp-2">{guide.description}</p>
                        )}
                        <FlexLayout align="center" gap="sm" className="text-xs text-muted-light">
                          {guide.view_count && (
                            <FlexLayout align="center" gap="sm">
                              <Eye className="w-3 h-3" />
                              <span>{guide.view_count} views</span>
                            </FlexLayout>
                          )}
                        </FlexLayout>
                      </FlexLayout>
                    </UnifiedCard>
                  ))}
                </div>
              </div>
            </GridLayout>
          </motion.div>

          {/* Quick Actions using Unified Components */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-bold text-primary-light mb-6">Quick Actions</h2>
            <GridLayout cols={4} gap="md">
              <Link to="/activities">
                <UnifiedCard variant="elevated" className="p-6 text-center hover:shadow-lg transition-shadow">
                  <Calendar className="w-10 h-10 mx-auto mb-3 text-festival-orange" />
                  <h3 className="font-semibold text-primary-light mb-2">Activities</h3>
                  <p className="text-sm text-secondary-light">Browse and join festival activities</p>
                </UnifiedCard>
              </Link>
              <Link to="/famhub">
                <UnifiedCard variant="elevated" className="p-6 text-center hover:shadow-lg transition-shadow">
                  <Users className="w-10 h-10 mx-auto mb-3 text-festival-orange" />
                  <h3 className="font-semibold text-primary-light mb-2">FamHub</h3>
                  <p className="text-sm text-secondary-light">Connect with your festival family</p>
                </UnifiedCard>
              </Link>
              <Link to="/discover">
                <UnifiedCard variant="elevated" className="p-6 text-center hover:shadow-lg transition-shadow">
                  <Music className="w-10 h-10 mx-auto mb-3 text-festival-orange" />
                  <h3 className="font-semibold text-primary-light mb-2">Discover</h3>
                  <p className="text-sm text-secondary-light">Find new festivals and events</p>
                </UnifiedCard>
              </Link>
              <Link to="/profile">
                <UnifiedCard variant="elevated" className="p-6 text-center hover:shadow-lg transition-shadow">
                  <Star className="w-10 h-10 mx-auto mb-3 text-festival-orange" />
                  <h3 className="font-semibold text-primary-light mb-2">Profile</h3>
                  <p className="text-sm text-secondary-light">Manage your festival profile</p>
                </UnifiedCard>
              </Link>
            </GridLayout>
          </motion.div>
        </PageWrapper>

        {/* Development-only Mobile UX Testing Tool */}
        <MobileUXTester />
      </div>
    </>
  );
};

export default AuthenticatedHome;
