import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal, AlertCircle, RefreshCw, Search, Filter, Calendar, MapPin, Users, Clock, Heart, Share2, ChevronRight, Star, Music, Zap, Trophy } from 'lucide-react';
import { ErrorBoundary } from 'react-error-boundary';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { toast } from 'react-hot-toast';
import { simulateHapticFeedback, isMobileViewport, createTouch<PERSON><PERSON><PERSON> } from '../utils/mobileUX';
import MobileUXTester from '../components/testing/MobileUXTester';
import { useActivitiesWithDetails } from '@/hooks/useActivitiesWithDetails';
import { ActivityWithDetails } from '@/types/activities';
import { useUserInteractions } from '@/hooks/useUserInteractions';
import { ActivityDetailsModal } from '@/components/activities/ActivityDetailsModal';
import { ActivityType } from '@/types/enums';
// Import unified design system components
import {
  PageWrapper,
  UnifiedCard,
  UnifiedButton,
  UnifiedBadge,
  GridLayout,
  FlexLayout
} from '@/components/design-system';

// Error fallback component for Activities page using unified design system
const ActivitiesErrorFallback = ({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => (
  <PageWrapper title="Error" variant="default">
    <UnifiedCard variant="outlined" priority="high" className="p-6 text-center">
      <FlexLayout direction="col" gap="md" align="center">
        <AlertCircle className="h-12 w-12 text-priority-high" />
        <h2 className="text-2xl font-bold text-primary-light">Unable to Load Activities</h2>
        <p className="text-secondary-light">
          We're having trouble loading the activities. This might be a temporary issue.
        </p>
        <div className="bg-priority-high-bg rounded-md p-4 border border-priority-high-border">
          <p className="text-sm text-priority-high">{error.message}</p>
        </div>
        <UnifiedButton onClick={resetErrorBoundary} variant="primary">
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </UnifiedButton>
      </FlexLayout>
    </UnifiedCard>
  </PageWrapper>
);

// Mock activity data for demonstration
const mockActivities = {
  meetup: [
    {
      id: '1',
      title: 'Pre-Festival Meetup',
      description: 'Connect with fellow festival-goers before the main event starts. Share tips, make friends, and plan your festival experience together.',
      type: 'meetup',
      time: '2:00 PM',
      location: 'Main Entrance',
      attendees: 24,
      capacity: 50,
      difficulty: 'Beginner',
      tags: ['Social', 'Networking'],
      featured: true,
      image: null
    },
    {
      id: '2',
      title: 'Artist Meet & Greet',
      description: 'Exclusive opportunity to meet your favorite artists, get autographs, and take photos.',
      type: 'meetup',
      time: '4:30 PM',
      location: 'VIP Tent',
      attendees: 12,
      capacity: 20,
      difficulty: 'All Levels',
      tags: ['Artists', 'Exclusive'],
      featured: false,
      image: null
    }
  ],
  daily: [
    {
      id: '3',
      title: 'Morning Yoga Session',
      description: 'Start your festival day with a peaceful yoga session to energize your body and mind.',
      type: 'wellness',
      time: '8:00 AM',
      location: 'Wellness Area',
      attendees: 18,
      capacity: 30,
      difficulty: 'Beginner',
      tags: ['Wellness', 'Morning'],
      featured: true,
      image: null
    },
    {
      id: '4',
      title: 'Food Truck Tour',
      description: 'Discover the best food vendors at the festival with our guided culinary tour.',
      type: 'food',
      time: '12:00 PM',
      location: 'Food Court',
      attendees: 35,
      capacity: 40,
      difficulty: 'All Levels',
      tags: ['Food', 'Tour'],
      featured: false,
      image: null
    }
  ],
  challenges: [
    {
      id: '5',
      title: 'Festival Photo Challenge',
      description: 'Capture the best moments of the festival and compete for amazing prizes.',
      type: 'challenge',
      time: 'All Day',
      location: 'Festival Grounds',
      attendees: 156,
      capacity: 200,
      difficulty: 'All Levels',
      tags: ['Photography', 'Competition'],
      featured: true,
      image: null
    },
    {
      id: '6',
      title: 'Dance Battle',
      description: 'Show off your moves in our epic dance battle competition.',
      type: 'challenge',
      time: '6:00 PM',
      location: 'Main Stage',
      attendees: 89,
      capacity: 100,
      difficulty: 'Intermediate',
      tags: ['Dance', 'Competition'],
      featured: false,
      image: null
    }
  ],
  upcoming: [
    {
      id: '7',
      title: 'Sunset Meditation',
      description: 'Wind down your festival day with a peaceful meditation session as the sun sets.',
      type: 'wellness',
      time: '7:30 PM',
      location: 'Hill Overlook',
      attendees: 42,
      capacity: 60,
      difficulty: 'Beginner',
      tags: ['Wellness', 'Sunset'],
      featured: true,
      image: null
    },
    {
      id: '8',
      title: 'Late Night Jam Session',
      description: 'Join fellow musicians for an impromptu jam session under the stars.',
      type: 'music',
      time: '10:00 PM',
      location: 'Acoustic Stage',
      attendees: 28,
      capacity: 50,
      difficulty: 'Intermediate',
      tags: ['Music', 'Jam'],
      featured: false,
      image: null
    }
  ]
};

/**
 * Enhanced Mobile-First Activities page
 */
const Activities: React.FC = () => {
  // Real database integration
  const { activitiesWithDetails, isLoading: dbLoading, error: dbError, refetch } = useActivitiesWithDetails({});

  // Mobile state management
  const [isMobile, setIsMobile] = useState(false);
  const [activeTab, setActiveTab] = useState('meetup');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Check if mobile viewport
  useEffect(() => {
    const checkMobile = () => setIsMobile(isMobileViewport());
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle pull-to-refresh with real data
  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    simulateHapticFeedback('medium');

    try {
      await refetch();
      toast.success('Activities refreshed!');
    } catch (error) {
      console.error('Refresh failed:', error);
      toast.error('Failed to refresh activities');
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing, refetch]);

  // Filter activities based on search and filters using real database data
  const filteredActivities = useMemo(() => {
    if (!activitiesWithDetails) return [];

    // Filter by tab type
    let activities = activitiesWithDetails;
    if (activeTab !== 'all') {
      activities = activitiesWithDetails.filter(activity => {
        switch (activeTab) {
          case 'meetup':
            return activity.type === ActivityType.MEETUP;
          case 'daily':
            return activity.type === ActivityType.WORKSHOP || activity.type === ActivityType.SOCIAL || activity.type === ActivityType.FOOD || activity.type === ActivityType.OTHER;
          case 'challenges':
            return activity.type === ActivityType.GAME || activity.type === ActivityType.PERFORMANCE;
          case 'upcoming':
            return activity.start_date ? new Date(activity.start_date) > new Date() : false;
          default:
            return true;
        }
      });
    }

    // Apply search and other filters
    return activities.filter(activity => {
      const matchesSearch = activity.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           activity.description?.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesSearch;
    });
  }, [activitiesWithDetails, activeTab, searchQuery]);

  // Handle tab change with haptic feedback
  const handleTabChange = useCallback((value: string) => {
    simulateHapticFeedback('light');
    setActiveTab(value);
  }, []);

  if (dbLoading) {
    return (
      <PageWrapper title="Loading Activities..." variant="default">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <UnifiedCard variant="elevated" className="p-8 text-center">
            <FlexLayout direction="col" align="center" gap="md">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Music className="w-12 h-12 text-festival-orange" />
              </motion.div>
              <LoadingSpinner size="lg" variant="dots" />
              <p className="text-secondary-light">
                {isMobile ? 'Loading activities...' : 'Loading festival activities...'}
              </p>
            </FlexLayout>
          </UnifiedCard>
        </motion.div>
      </PageWrapper>
    );
  }

  return (
    <ErrorBoundary FallbackComponent={ActivitiesErrorFallback}>
      <PageWrapper
        title="Activities"
        subtitle="Discover and join festival activities"
        actions={
          <UnifiedButton
            variant="ghost"
            size="sm"
            onClick={() => {
              // Refresh functionality
              toast.success('Refreshing activities...');
            }}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </UnifiedButton>
        }
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
        >
          {/* Search and Filter Section */}
          <UnifiedCard variant="elevated" className="p-6 mb-6">
            <FlexLayout direction="col" gap="md">
              {/* Search Bar with Unified Design */}
              <FlexLayout align="center" gap="sm">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-light" />
                  <Input
                    type="text"
                    placeholder={isMobile ? "Search activities..." : "Search festival activities..."}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-card border-neutral-200 text-primary-light placeholder:text-muted-light focus:border-festival-orange rounded-lg"
                    style={{ fontSize: '16px' }} // Prevents zoom on iOS
                  />
                </div>
                <UnifiedButton
                  variant="outline"
                  size="md"
                  onClick={() => {
                    setShowFilters(!showFilters);
                    simulateHapticFeedback('light');
                  }}
                >
                  <Filter className="w-4 h-4" />
                </UnifiedButton>
              </FlexLayout>
            </FlexLayout>
          </UnifiedCard>

            {/* Activity Tabs with Unified Design */}
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full mb-6">
              <TabsList className="grid w-full grid-cols-4 bg-card border border-neutral-200 p-1 component-base">
                <TabsTrigger
                  value="meetup"
                  className="data-[state=active]:bg-festival-gradient data-[state=active]:text-primary-dark text-secondary-light hover:text-primary-light rounded-md touch-target text-xs sm:text-sm min-h-[44px] px-2 sm:px-3"
                  style={{ touchAction: 'manipulation' }}
                >
                  <div className="flex items-center gap-1 sm:gap-2">
                    <Users className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs sm:text-sm">Meetups</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="daily"
                  className="data-[state=active]:bg-festival-gradient data-[state=active]:text-primary-dark text-secondary-light hover:text-primary-light rounded-md touch-target text-xs sm:text-sm min-h-[44px] px-2 sm:px-3"
                  style={{ touchAction: 'manipulation' }}
                >
                  <div className="flex items-center gap-1 sm:gap-2">
                    <Calendar className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs sm:text-sm">Daily</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="challenges"
                  className="data-[state=active]:bg-festival-gradient data-[state=active]:text-primary-dark text-secondary-light hover:text-primary-light rounded-md touch-target text-xs sm:text-sm min-h-[44px] px-2 sm:px-3"
                  style={{ touchAction: 'manipulation' }}
                >
                  <div className="flex items-center gap-1 sm:gap-2">
                    <Trophy className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="hidden sm:inline text-xs sm:text-sm">Challenges</span>
                    <span className="sm:hidden text-xs">Compete</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="upcoming"
                  className="data-[state=active]:bg-festival-gradient data-[state=active]:text-primary-dark text-secondary-light hover:text-primary-light rounded-md touch-target text-xs sm:text-sm min-h-[44px] px-2 sm:px-3"
                  style={{ touchAction: 'manipulation' }}
                >
                  <div className="flex items-center gap-1 sm:gap-2">
                    <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="hidden sm:inline text-xs sm:text-sm">Upcoming</span>
                    <span className="sm:hidden text-xs">Later</span>
                  </div>
                </TabsTrigger>
                  </TabsList>

                  {/* Enhanced Activity Content */}
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeTab}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      <EnhancedActivityTab
                        activities={filteredActivities}
                        activeTab={activeTab}
                        isMobile={isMobile}
                        showFilters={showFilters}
                        selectedDifficulty={selectedDifficulty}
                        setSelectedDifficulty={setSelectedDifficulty}
                        selectedTags={selectedTags}
                        setSelectedTags={setSelectedTags}
                        searchQuery={searchQuery}
                      />
                    </motion.div>
                  </AnimatePresence>
              </Tabs>

              {/* Activity Details Modal */}
              <ActivityDetailsModal
                activity={activity}
                isOpen={showDetails}
                onClose={() => setShowDetails(false)}
              />
            </motion.div>

            {/* Development-only Mobile UX Testing Tool */}
            <MobileUXTester />
          </PageWrapper>
        </ErrorBoundary>
  );
};

// Enhanced Activity Tab Component with Mobile-First Design
interface EnhancedActivityTabProps {
  activities: any[];
  activeTab: string;
  isMobile: boolean;
  showFilters: boolean;
  selectedDifficulty: string;
  setSelectedDifficulty: (difficulty: string) => void;
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  searchQuery?: string;
}

const EnhancedActivityTab: React.FC<EnhancedActivityTabProps> = ({
  activities,
  activeTab,
  isMobile,
  showFilters,
  selectedDifficulty,
  setSelectedDifficulty,
  selectedTags,
  setSelectedTags,
  searchQuery
}) => {
  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'meetup': return Users;
      case 'daily': return Calendar;
      case 'challenges': return Trophy;
      case 'upcoming': return Clock;
      default: return Music;
    }
  };

  const getTabTitle = (tab: string) => {
    switch (tab) {
      case 'meetup': return 'Festival Meetups';
      case 'daily': return 'Daily Activities';
      case 'challenges': return 'Festival Challenges';
      case 'upcoming': return 'Upcoming Activities';
      default: return 'Activities';
    }
  };

  const getTabDescription = (tab: string) => {
    switch (tab) {
      case 'meetup': return 'Connect with other festival-goers at organized meetups';
      case 'daily': return 'Discover daily activities happening at festivals';
      case 'challenges': return 'Participate in fun challenges with other attendees';
      case 'upcoming': return 'See what\'s coming up at your favorite festivals';
      default: return 'Explore festival activities';
    }
  };

  const IconComponent = getTabIcon(activeTab);

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Tab Header */}
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-lg flex items-center justify-center">
          <IconComponent className="w-4 h-4 text-purple-400" />
        </div>
        <div>
          <h3 className="text-lg sm:text-xl font-semibold">{getTabTitle(activeTab)}</h3>
          <p className="text-white/70 text-sm sm:text-base">{getTabDescription(activeTab)}</p>
        </div>
      </div>

      {/* Mobile Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white/5 rounded-xl p-4 border border-white/10"
          >
            <h4 className="text-sm font-medium text-white/80 mb-3">Filters</h4>
            <div className="space-y-3">
              {/* Difficulty Filter */}
              <div>
                <label className="block text-xs font-medium text-white/60 mb-2">Difficulty</label>
                <div className="flex flex-wrap gap-2">
                  {['all', 'Beginner', 'Intermediate', 'Advanced'].map((difficulty) => (
                    <motion.button
                      key={difficulty}
                      onClick={() => {
                        setSelectedDifficulty(difficulty);
                        simulateHapticFeedback('light');
                      }}
                      className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-colors ${
                        selectedDifficulty === difficulty
                          ? 'bg-purple-600 text-white'
                          : 'bg-white/10 text-white/70 hover:bg-white/20'
                      }`}
                      whileTap={{ scale: 0.95 }}
                    >
                      {difficulty === 'all' ? 'All Levels' : difficulty}
                    </motion.button>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Activities Grid */}
      {activities.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {activities.map((activity, index) => (
            <MobileActivityCard
              key={activity.id}
              activity={activity}
              index={index}
              isMobile={isMobile}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8 sm:py-12">
          <div className="w-16 h-16 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-4">
            <IconComponent className="w-8 h-8 text-white/40" />
          </div>
          <h4 className="text-lg font-medium text-white/60 mb-2">No activities found</h4>
          <p className="text-white/40 text-sm">
            {searchQuery ? 'Try adjusting your search terms' : 'Check back later for new activities'}
          </p>
        </div>
      )}
    </div>
  );
};

// Mobile-Optimized Activity Card Component
interface MobileActivityCardProps {
  activity: ActivityWithDetails;
  index: number;
  isMobile: boolean;
}

const MobileActivityCard: React.FC<MobileActivityCardProps> = ({ activity, index, isMobile }) => {
  const [showDetails, setShowDetails] = useState(false);

  // Use real backend functionality for user interactions
  const {
    isJoined,
    isFavorited,
    joinLoading,
    favoriteLoading,
    toggleJoin,
    toggleFavorite,
    recordView,
  } = useUserInteractions(activity.id || '');

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'meetup': return Users;
      case 'wellness': return Heart;
      case 'food': return Calendar;
      case 'challenge': return Trophy;
      case 'music': return Music;
      default: return Zap;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'meetup': return 'from-blue-500/20 to-blue-600/20';
      case 'wellness': return 'from-green-500/20 to-green-600/20';
      case 'food': return 'from-orange-500/20 to-orange-600/20';
      case 'challenge': return 'from-yellow-500/20 to-yellow-600/20';
      case 'music': return 'from-purple-500/20 to-purple-600/20';
      default: return 'from-gray-500/20 to-gray-600/20';
    }
  };

  const IconComponent = getActivityIcon(activity.type);
  const colorClass = getActivityColor(activity.type);

  const handleJoin = useCallback(async () => {
    simulateHapticFeedback('medium');
    await toggleJoin();
  }, [toggleJoin]);

  const handleFavorite = useCallback(async () => {
    simulateHapticFeedback('light');
    await toggleFavorite();
  }, [toggleFavorite]);

  const handleDetails = useCallback(() => {
    simulateHapticFeedback('light');
    recordView();
    setShowDetails(true);
  }, [recordView]);

  const handleShare = useCallback(() => {
    simulateHapticFeedback('light');
    toast.success('Activity shared!');
  }, []);

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
        className="group"
        data-testid="activity-card"
      >
        <Card className="bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 transition-all duration-300 overflow-hidden text-white">
          <CardContent className="p-4 sm:p-5">
          {/* Card Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3">
              <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center`}>
                <IconComponent className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-sm sm:text-base leading-tight truncate">{activity.title}</h4>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="bg-white/10 text-white/80 text-xs px-2 py-0.5">
                    {activity.type}
                  </Badge>
                  {activity.is_featured && (
                    <Star className="w-3 h-3 text-yellow-400 fill-current" />
                  )}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-1">
              <motion.button
                onClick={handleFavorite}
                disabled={favoriteLoading}
                className={`p-1.5 rounded-lg transition-colors ${
                  isFavorited ? 'text-red-400' : 'text-white/50 hover:text-white/80'
                } ${favoriteLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                whileTap={{ scale: favoriteLoading ? 1 : 0.9 }}
              >
                {favoriteLoading ? (
                  <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
                )}
              </motion.button>
              <motion.button
                onClick={handleShare}
                className="p-1.5 rounded-lg text-white/50 hover:text-white/80 transition-colors"
                whileTap={{ scale: 0.9 }}
              >
                <Share2 className="w-4 h-4" />
              </motion.button>
            </div>
          </div>

          {/* Description */}
          <p className="text-white/70 text-sm leading-relaxed mb-4 line-clamp-2">
            {activity.description}
          </p>

          {/* Activity Details */}
          <div className="space-y-2 mb-4">
            {activity.start_date && (
              <div className="flex items-center gap-2 text-xs text-white/60">
                <Clock className="w-3 h-3" />
                <span>{new Date(activity.start_date).toLocaleDateString()}</span>
              </div>
            )}
            {activity.location && (
              <div className="flex items-center gap-2 text-xs text-white/60">
                <MapPin className="w-3 h-3" />
                <span>{activity.location}</span>
              </div>
            )}
            <div className="flex items-center gap-2 text-xs text-white/60">
              <Users className="w-3 h-3" />
              <span>Activity available</span>
            </div>
          </div>

          {/* Type Badge */}
          <div className="flex flex-wrap gap-1 mb-4">
            <Badge variant="outline" className="border-white/20 text-white/60 text-xs px-2 py-0.5">
              {activity.type}
            </Badge>
            {activity.is_featured && (
              <Badge variant="outline" className="border-yellow-400/20 text-yellow-400 text-xs px-2 py-0.5">
                Featured
              </Badge>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <motion.button
              onClick={handleJoin}
              disabled={joinLoading}
              className={`flex-1 inline-flex items-center justify-center gap-2 touch-target px-3 py-2.5 rounded-lg font-medium text-sm transition-all duration-300 ${
                isJoined
                  ? 'bg-green-600/20 text-green-400 border border-green-600/30'
                  : 'bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white'
              } ${joinLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              whileHover={{ scale: joinLoading ? 1 : 1.02 }}
              whileTap={{ scale: joinLoading ? 1 : 0.98 }}
              style={{ touchAction: 'manipulation' }}
            >
              {joinLoading ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <span>{isJoined ? 'Joined' : 'Join Activity'}</span>
              )}
            </motion.button>

            <motion.button
              onClick={handleDetails}
              className="inline-flex items-center justify-center gap-2 touch-target px-3 py-2.5 bg-white bg-opacity-10 hover:bg-white hover:bg-opacity-20 rounded-lg text-white font-medium text-sm transition-all duration-300"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              style={{ touchAction: 'manipulation' }}
            >
              <span>Details</span>
              <ChevronRight className="w-3 h-3" />
            </motion.button>
          </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Activity Details Modal */}
      <ActivityDetailsModal
        activity={activity}
        isOpen={showDetails}
        onClose={() => setShowDetails(false)}
      />
    </>
  );
};

// Legacy ActivityTab component for backward compatibility
interface ActivityTabProps {
  title: string;
  description: string;
}

const ActivityTab: React.FC<ActivityTabProps> = ({ title, description }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold mb-2">{title}</h3>
        <p className="text-white/70 mb-4">{description}</p>
      </div>

      <Alert className="bg-purple-700/30 border-purple-700 text-white">
        <Terminal className="h-4 w-4" />
        <AlertTitle>Coming Soon</AlertTitle>
        <AlertDescription>
          Activities functionality will be available in a future update.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="bg-white/5 border-white/10 text-white">
            <CardContent className="p-4">
              <div className="h-32 flex items-center justify-center">
                <p className="text-white/50">Activity placeholder {i}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Activities;
