/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        // Festival Family 2025 Design System - Mocha <PERSON>usse Inspired
        primary: {
          50: 'var(--color-primary-50)',
          100: 'var(--color-primary-100)',
          200: 'var(--color-primary-200)',
          300: 'var(--color-primary-300)',
          400: 'var(--color-primary-400)',
          500: 'var(--color-primary-500)', // Main Mocha <PERSON>usse
          600: 'var(--color-primary-600)',
          700: 'var(--color-primary-700)',
          800: 'var(--color-primary-800)',
          900: 'var(--color-primary-900)',
          DEFAULT: 'var(--color-primary-500)',
        },

        // Festival Accent Colors
        'festival-orange': {
          50: 'var(--color-accent-orange-50)',
          100: 'var(--color-accent-orange-100)',
          200: 'var(--color-accent-orange-200)',
          300: 'var(--color-accent-orange-300)',
          400: 'var(--color-accent-orange-400)',
          500: 'var(--color-accent-orange-500)',
          600: 'var(--color-accent-orange-600)',
          700: 'var(--color-accent-orange-700)',
          800: 'var(--color-accent-orange-800)',
          900: 'var(--color-accent-orange-900)',
          DEFAULT: 'var(--color-accent-orange-500)',
        },
        'festival-purple': {
          50: 'var(--color-accent-purple-50)',
          100: 'var(--color-accent-purple-100)',
          200: 'var(--color-accent-purple-200)',
          300: 'var(--color-accent-purple-300)',
          400: 'var(--color-accent-purple-400)',
          500: 'var(--color-accent-purple-500)',
          600: 'var(--color-accent-purple-600)',
          700: 'var(--color-accent-purple-700)',
          800: 'var(--color-accent-purple-800)',
          900: 'var(--color-accent-purple-900)',
          DEFAULT: 'var(--color-accent-purple-500)',
        },
        'festival-pink': {
          50: 'var(--color-accent-pink-50)',
          100: 'var(--color-accent-pink-100)',
          200: 'var(--color-accent-pink-200)',
          300: 'var(--color-accent-pink-300)',
          400: 'var(--color-accent-pink-400)',
          500: 'var(--color-accent-pink-500)',
          600: 'var(--color-accent-pink-600)',
          700: 'var(--color-accent-pink-700)',
          800: 'var(--color-accent-pink-800)',
          900: 'var(--color-accent-pink-900)',
          DEFAULT: 'var(--color-accent-pink-500)',
        },
        'festival-teal': {
          50: 'var(--color-accent-teal-50)',
          100: 'var(--color-accent-teal-100)',
          200: 'var(--color-accent-teal-200)',
          300: 'var(--color-accent-teal-300)',
          400: 'var(--color-accent-teal-400)',
          500: 'var(--color-accent-teal-500)',
          600: 'var(--color-accent-teal-600)',
          700: 'var(--color-accent-teal-700)',
          800: 'var(--color-accent-teal-800)',
          900: 'var(--color-accent-teal-900)',
          DEFAULT: 'var(--color-accent-teal-500)',
        },

        // Warm Neutrals
        neutral: {
          50: 'var(--color-neutral-50)',
          100: 'var(--color-neutral-100)',
          200: 'var(--color-neutral-200)',
          300: 'var(--color-neutral-300)',
          400: 'var(--color-neutral-400)',
          500: 'var(--color-neutral-500)',
          600: 'var(--color-neutral-600)',
          700: 'var(--color-neutral-700)',
          800: 'var(--color-neutral-800)',
          900: 'var(--color-neutral-900)',
        },

        // Semantic Colors
        success: {
          50: 'var(--color-success-50)',
          500: 'var(--color-success-500)',
          600: 'var(--color-success-600)',
          DEFAULT: 'var(--color-success-500)',
        },
        warning: {
          50: 'var(--color-warning-50)',
          500: 'var(--color-warning-500)',
          600: 'var(--color-warning-600)',
          DEFAULT: 'var(--color-warning-500)',
        },
        error: {
          50: 'var(--color-error-50)',
          500: 'var(--color-error-500)',
          600: 'var(--color-error-600)',
          DEFAULT: 'var(--color-error-500)',
        },
      },
      fontFamily: {
        'manrope': ['Manrope Variable', 'sans-serif'],
        'outfit': ['Outfit Variable', 'sans-serif'],
        'space-grotesk': ['Space Grotesk', 'sans-serif'],
      },
      spacing: {
        '0': 'var(--spacing-0)',
        '1': 'var(--spacing-1)',
        '2': 'var(--spacing-2)',
        '3': 'var(--spacing-3)',
        '4': 'var(--spacing-4)',
        '5': 'var(--spacing-5)',
        '6': 'var(--spacing-6)',
        '8': 'var(--spacing-8)',
        '10': 'var(--spacing-10)',
        '12': 'var(--spacing-12)',
        '16': 'var(--spacing-16)',
        '20': 'var(--spacing-20)',
        '24': 'var(--spacing-24)',
      },
      fontSize: {
        'xs': 'var(--font-size-xs)',
        'sm': 'var(--font-size-sm)',
        'base': 'var(--font-size-base)',
        'lg': 'var(--font-size-lg)',
        'xl': 'var(--font-size-xl)',
        '2xl': 'var(--font-size-2xl)',
        '3xl': 'var(--font-size-3xl)',
        '4xl': 'var(--font-size-4xl)',
        '5xl': 'var(--font-size-5xl)',
        '6xl': 'var(--font-size-6xl)',
      },
      backgroundImage: {
        'gradient-festival': 'var(--gradient-festival)',
        'gradient-warm': 'var(--gradient-warm)',
        'gradient-cool': 'var(--gradient-cool)',
        'gradient-sunset': 'var(--gradient-sunset)',
      },
      boxShadow: {
        'sm': 'var(--shadow-sm)',
        'DEFAULT': 'var(--shadow-base)',
        'md': 'var(--shadow-md)',
        'lg': 'var(--shadow-lg)',
        'xl': 'var(--shadow-xl)',
        '2xl': 'var(--shadow-2xl)',
        'festival-glow': '0 0 20px rgba(236, 72, 153, 0.3)',
        'warm-glow': '0 0 15px rgba(176, 137, 104, 0.3)',
      },
      animation: {
        'slide-in': 'slide-in 0.3s ease-out',
        'slide-out': 'slide-out 0.3s ease-in',
        'fade-in': 'fade-in 0.3s ease-out',
        'fade-out': 'fade-out 0.3s ease-in',
        'bounce-subtle': 'bounce-subtle 2s infinite',
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'gradient': 'gradient 15s ease infinite',
      },
      keyframes: {
        'slide-in': {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        'slide-out': {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'fade-out': {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        'bounce-subtle': {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        'accordion-down': {
          from: { height: 0 },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: 0 },
        },
        'gradient': {
          '0%, 100%': { 'background-position': '0% 50%' },
          '50%': { 'background-position': '100% 50%' },
        },
      },
      borderRadius: {
        'festival': '1rem',  // More rounded corners
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
    require('tailwind-scrollbar-hide'),
  ],
}
