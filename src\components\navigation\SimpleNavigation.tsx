import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../providers/ConsolidatedAuthProvider';
import UnifiedAuthNavigation from './UnifiedAuthNavigation';
import { globalHamburgerMenu } from '../../hooks/useHamburgerMenu';
import {
  Shield,
  ChevronDown,
  LayoutDashboard,
  Users,
  Calendar,
  Tent,
  MessageSquare,
  Bell,
  Eye,
  ArrowLeft,
  Menu
} from 'lucide-react';

/**
 * Simple navigation component for the app
 * This provides basic navigation between the main pages
 */
const SimpleNavigation: React.FC = () => {
  const { user, signOut, isAdmin, loading } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [adminDropdownOpen, setAdminDropdownOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'admin' | 'user'>('user');

  // Debug authentication state for desktop
  useEffect(() => {
    console.log('SimpleNavigation - Auth State:', {
      user: !!user,
      isAdmin,
      loading,
      userEmail: user?.email
    });
  }, [user, isAdmin, loading]);

  // Close admin dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (adminDropdownOpen && !target.closest('.admin-dropdown-container')) {
        setAdminDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [adminDropdownOpen]);

  // Navigation items
  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Activities', path: '/activities' },
    { name: 'FamHub', path: '/famhub' },
    { name: 'Discover', path: '/discover' },
    { name: 'Profile', path: '/profile' },
  ];

  // Admin quick access items
  const adminQuickAccess = [
    { name: 'Dashboard', path: '/admin', icon: LayoutDashboard },
    { name: 'Users', path: '/admin/users', icon: Users },
    { name: 'Events', path: '/admin/events', icon: Calendar },
    { name: 'Festivals', path: '/admin/festivals', icon: Tent },
    { name: 'Activities', path: '/admin/activities', icon: MessageSquare },
    { name: 'Announcements', path: '/admin/announcements', icon: Bell },
  ];

  // Check if a path is active
  const isActive = (path: string) => location.pathname === path;

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Close mobile menu when a link is clicked
  const handleNavClick = () => {
    setMobileMenuOpen(false);
  };

  // Detect if currently in admin section
  const isInAdminSection = location.pathname.startsWith('/admin');

  // Toggle admin dropdown
  const toggleAdminDropdown = () => {
    setAdminDropdownOpen(!adminDropdownOpen);
  };

  // Handle view mode toggle
  const toggleViewMode = () => {
    if (viewMode === 'user') {
      setViewMode('admin');
      navigate('/admin');
    } else {
      setViewMode('user');
      navigate('/');
    }
  };

  // Handle admin quick access navigation
  const handleAdminNavigation = (path: string) => {
    navigate(path);
    setAdminDropdownOpen(false);
    setViewMode('admin');
  };

  return (
    <nav className="backdrop-blur-md border-b border-white/10 sticky top-0 z-40" style={{ backgroundColor: 'var(--color-bg-secondary)' }}>
      <div className="container mx-auto px-4">
        <div className="flex justify-between h-20 items-center">
          {/* Logo and brand */}
          <div className="flex items-center">
            <Link to="/" className="text-xl font-bold" style={{ color: 'var(--color-text-primary-dark)' }}>
              Festival Family
            </Link>
          </div>

          {/* Desktop navigation */}
          <div className="hidden md:flex items-center space-x-4">
            {navItems.map(item => (
              <Link
                key={item.path}
                to={item.path}
                className={`
                  px-3 py-2 rounded-md text-sm font-medium transition-colors
                  ${isActive(item.path)
                    ? 'bg-purple-700'
                    : 'hover:bg-white/10'}
                `}
                style={{
                  color: isActive(item.path)
                    ? 'var(--color-text-primary-dark)'
                    : 'var(--color-text-secondary-dark)'
                }}
              >
                {item.name}
              </Link>
            ))}

            {/* Enhanced Admin Navigation with Dropdown */}
            {isAdmin && (
              <div className="relative admin-dropdown-container">
                {/* Admin Indicator with Shield Icon */}
                <button
                  onClick={toggleAdminDropdown}
                  className={`
                    flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors
                    ${isInAdminSection
                      ? 'bg-purple-700 text-white'
                      : 'text-white/70 hover:text-white hover:bg-white/10'}
                  `}
                >
                  <Shield className="w-4 h-4" />
                  <span>Admin</span>
                  <ChevronDown className={`w-4 h-4 transition-transform ${adminDropdownOpen ? 'rotate-180' : ''}`} />
                </button>

                {/* Admin Quick Access Dropdown */}
                {adminDropdownOpen && (
                  <div className="absolute top-full left-0 mt-2 w-56 bg-white/10 backdrop-blur-md border border-white/20 rounded-lg shadow-lg z-50">
                    <div className="p-2">
                      {/* View Mode Toggle */}
                      <button
                        onClick={toggleViewMode}
                        className="w-full flex items-center gap-2 px-3 py-2 text-sm text-white/70 hover:text-white hover:bg-white/10 rounded-md transition-colors"
                      >
                        {isInAdminSection ? (
                          <>
                            <Eye className="w-4 h-4" />
                            <span>View as User</span>
                          </>
                        ) : (
                          <>
                            <ArrowLeft className="w-4 h-4" />
                            <span>Back to Admin</span>
                          </>
                        )}
                      </button>

                      <div className="border-t border-white/10 my-2"></div>

                      {/* Quick Access Items */}
                      {adminQuickAccess.map((item) => {
                        const Icon = item.icon;
                        return (
                          <button
                            key={item.path}
                            onClick={() => handleAdminNavigation(item.path)}
                            className={`
                              w-full flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors
                              ${isActive(item.path)
                                ? 'bg-purple-600/50 text-white'
                                : 'text-white/70 hover:text-white hover:bg-white/10'}
                            `}
                          >
                            <Icon className="w-4 h-4" />
                            <span>{item.name}</span>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Desktop Hamburger Menu Button */}
            <button
              onClick={() => globalHamburgerMenu.toggle()}
              className="btn-icon ml-4"
              title="Open menu"
              aria-label="Open menu"
            >
              <Menu className="w-5 h-5 text-white/70" />
            </button>

            {/* Authentication State Display - Using unified component */}
            <div className="ml-6 pl-6 border-l border-white/20">
              <UnifiedAuthNavigation variant="header" />
            </div>
          </div>

          {/* Mobile user info - Removed to prevent duplicate sign out buttons */}
          {/* Mobile authentication is handled by ModernBottomNav hamburger menu */}
        </div>
      </div>

      {/* We're removing the mobile menu dropdown since we have BottomNav for mobile navigation */}
    </nav>
  );
};

export default SimpleNavigation;
