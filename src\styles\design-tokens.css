/* 
 * Festival Family Design System 2025
 * Modern color palette inspired by Pantone Mocha Mouss<PERSON> with vibrant festival accents
 */

:root {
  /* === PRIMARY COLORS === */
  /* <PERSON><PERSON>e - Pantone Color of the Year 2025 */
  --color-primary-50: #faf8f6;
  --color-primary-100: #f4f0eb;
  --color-primary-200: #e8ddd2;
  --color-primary-300: #d7c4b0;
  --color-primary-400: #c4a688;
  --color-primary-500: #b08968; /* Main Mocha Mousse */
  --color-primary-600: #9d7a5e;
  --color-primary-700: #826450;
  --color-primary-800: #6b5244;
  --color-primary-900: #574439;

  /* === FESTIVAL ACCENT COLORS === */
  /* Vibrant Orange - Energy & Excitement */
  --color-accent-orange-50: #fff7ed;
  --color-accent-orange-100: #ffedd5;
  --color-accent-orange-200: #fed7aa;
  --color-accent-orange-300: #fdba74;
  --color-accent-orange-400: #fb923c;
  --color-accent-orange-500: #f97316; /* Main Orange */
  --color-accent-orange-600: #ea580c;
  --color-accent-orange-700: #c2410c;
  --color-accent-orange-800: #9a3412;
  --color-accent-orange-900: #7c2d12;

  /* Electric Purple - Music & Creativity */
  --color-accent-purple-50: #faf5ff;
  --color-accent-purple-100: #f3e8ff;
  --color-accent-purple-200: #e9d5ff;
  --color-accent-purple-300: #d8b4fe;
  --color-accent-purple-400: #c084fc;
  --color-accent-purple-500: #a855f7; /* Main Purple */
  --color-accent-purple-600: #9333ea;
  --color-accent-purple-700: #7c3aed;
  --color-accent-purple-800: #6b21a8;
  --color-accent-purple-900: #581c87;

  /* Sunset Pink - Community & Connection */
  --color-accent-pink-50: #fdf2f8;
  --color-accent-pink-100: #fce7f3;
  --color-accent-pink-200: #fbcfe8;
  --color-accent-pink-300: #f9a8d4;
  --color-accent-pink-400: #f472b6;
  --color-accent-pink-500: #ec4899; /* Main Pink */
  --color-accent-pink-600: #db2777;
  --color-accent-pink-700: #be185d;
  --color-accent-pink-800: #9d174d;
  --color-accent-pink-900: #831843;

  /* Electric Teal - Activities & Discovery */
  --color-accent-teal-50: #f0fdfa;
  --color-accent-teal-100: #ccfbf1;
  --color-accent-teal-200: #99f6e4;
  --color-accent-teal-300: #5eead4;
  --color-accent-teal-400: #2dd4bf;
  --color-accent-teal-500: #14b8a6; /* Main Teal */
  --color-accent-teal-600: #0d9488;
  --color-accent-teal-700: #0f766e;
  --color-accent-teal-800: #115e59;
  --color-accent-teal-900: #134e4a;

  /* === NEUTRAL COLORS === */
  /* Warm Neutrals - Better than pure white/gray */
  --color-neutral-50: #fafaf9;
  --color-neutral-100: #f5f5f4;
  --color-neutral-200: #e7e5e4;
  --color-neutral-300: #d6d3d1;
  --color-neutral-400: #a8a29e;
  --color-neutral-500: #78716c;
  --color-neutral-600: #57534e;
  --color-neutral-700: #44403c;
  --color-neutral-800: #292524;
  --color-neutral-900: #1c1917;

  /* === SEMANTIC COLORS === */
  /* Success - Green */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  /* Warning - Amber */
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;

  /* Error - Red */
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;

  /* === SPACING SCALE === */
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */
  --spacing-24: 6rem;     /* 96px */

  /* === TYPOGRAPHY SCALE === */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* === BORDER RADIUS === */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* === SHADOWS === */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* === GRADIENTS === */
  --gradient-festival: linear-gradient(135deg, var(--color-accent-orange-500) 0%, var(--color-accent-pink-500) 50%, var(--color-accent-purple-500) 100%);
  --gradient-warm: linear-gradient(135deg, var(--color-primary-400) 0%, var(--color-accent-orange-400) 100%);
  --gradient-cool: linear-gradient(135deg, var(--color-accent-teal-500) 0%, var(--color-accent-purple-500) 100%);
  --gradient-sunset: linear-gradient(135deg, var(--color-accent-orange-400) 0%, var(--color-accent-pink-400) 50%, var(--color-accent-purple-400) 100%);
}

/* === DARK MODE OVERRIDES === */
@media (prefers-color-scheme: dark) {
  :root {
    /* Adjust colors for dark mode while maintaining warmth */
    --color-neutral-50: #1c1917;
    --color-neutral-100: #292524;
    --color-neutral-200: #44403c;
    --color-neutral-300: #57534e;
    --color-neutral-400: #78716c;
    --color-neutral-500: #a8a29e;
    --color-neutral-600: #d6d3d1;
    --color-neutral-700: #e7e5e4;
    --color-neutral-800: #f5f5f4;
    --color-neutral-900: #fafaf9;
  }
}

/* === UTILITY CLASSES === */
.text-festival-gradient {
  background: var(--gradient-festival);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-festival-gradient {
  background: var(--gradient-festival);
}

.bg-warm-gradient {
  background: var(--gradient-warm);
}

.bg-cool-gradient {
  background: var(--gradient-cool);
}

.bg-sunset-gradient {
  background: var(--gradient-sunset);
}
